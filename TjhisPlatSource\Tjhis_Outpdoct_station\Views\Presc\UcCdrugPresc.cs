﻿using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Layout;
using PlatCommon.Common;
using PlatCommon.SysBase;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Windows.Forms;
using Tjhis.Outpdoct.Station.Common;
using Tjhis.Outpdoct.Station.Diagnosis.CommonSrv;
using Tjhis.Outpdoct.Station.Diagnosis.Views;
using Tjhis.Outpdoct.Station.Interface;
using Tjhis.Outpdoct.Station.Model;
using Tjhis.Outpdoct.Station.Views.CustomControls;
using Tjhis.Outpdoct.Station.Views.Print;

namespace Tjhis.Outpdoct.Station.Views.Presc
{
    public partial class UcCdrugPresc : UserControlTabBase
    {

        protected override string FileName => "UcCdrugPresc";
        public override event EventHandler SaveSuccess;
        protected override GridView[] GridViews => new GridView[]
        {
            this.gvDrug,
            //this.gvPresc,
            this.gvPrescList,
            this.gvTreatDetail,
            this.gvTreatMaster
        };
        protected override LayoutView[] LayoutView => new LayoutView[]
        {
            this.layoutView1
        };
        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            this.GCModel.Dock = DockStyle.Fill;
            this.gcDrug.Dock = DockStyle.Fill;

        }

        public void SetBusiness(IOrders orderBusiness, ICPresc prescBusiness, IOutpMr outpMrBusiness)
        {
            this.OrderBusiness = orderBusiness;
            this.PrescBusiness = prescBusiness;
            this.OutpMrBusiness = outpMrBusiness;
            this.diagnosisInputSrv = new DiagnosisInputSrv();

        }
        public void InitControlData()
        {
            this.InitLookUpEdit();
            this.initGridView();
        }


        public void BindOpspDiseData(string patientId)
        {
            DataTable dtOpspDise = CommonDict.GetOpspDise(patientId);
            luePespDise.Properties.DataSource = dtOpspDise;
            luePespDise.Properties.DisplayMember = "OPSP_DISE_NAME";
            luePespDise.Properties.ValueMember = "OPSP_DISE_CODE";
        }
        

        public void ChangePatient(List<OUTP_ORDERS_STANDARD> orders, OutpPatientInfo outpPatientInfo, OutpMr mrInfo)
        {
            this.Orders = orders;
            this.CurrentPatientInfo = outpPatientInfo;
            this.MrInfo = mrInfo;
            this.SetDiagnosis();
            this.InitPrescList();
            this.InitStorages();
        }
        public override void InitData(List<OUTP_ORDERS_STANDARD> orders,bool firstInit=true)
        {
            this.Orders = orders;
            this.CurrentPatientInfo = this.OrderBusiness.GetCurrentPatient();
            try
            {
                this.MrInfo = this.OrderBusiness.GetMrInfo();
            }
            catch (Exception e)
            {
                this.MrInfo = null;
            }

            this.LuDiagDesc.Properties.DataSource = this.OrderBusiness.GetDtDiagnosis();
            //this.SetDiagnosis();
            this.InitPrescList();
            BindOpspDiseData(CurrentPatientInfo.PATIENT_ID);
        }

        public override void SaveModel()
        {
            OUTP_ORDERS_STANDARD presc = this.GetEditModel<OUTP_ORDERS_STANDARD>(this.bsPrescList);
            List<OUTP_ORDERS_STANDARD> prescList = this.bsPresc.Cast<OUTP_ORDERS_STANDARD>().ToList();
            if (presc == null || prescList == null || prescList.Count == 0)
            {
                MessageBoxHelper.Info("没有需要保存的模板明细");
                return;
            }
            FrmSaveCdrugProject frmSaveCdrugProject = new FrmSaveCdrugProject();
            frmSaveCdrugProject.PrescBusiness = this.PrescBusiness;
            frmSaveCdrugProject.Presc = presc;
            frmSaveCdrugProject.PrescList = prescList;
            frmSaveCdrugProject.DeptCode = GlobalValue.DeptCode;
            frmSaveCdrugProject.AppCode = GlobalValue.AppCode;
            frmSaveCdrugProject.UserName = SystemParm.LoginUser.USER_NAME;
            frmSaveCdrugProject.ShowDialog();
            frmSaveCdrugProject.Dispose();
        }

        public override void NewPresc()
        {
            TxtPrescAttr.ReadOnly = false;
            if (this.PrescBusiness.isNeedSave(this.bsPrescList.List.Cast<OUTP_ORDERS_STANDARD>().ToList(), this.bsPresc.List.Cast<OUTP_ORDERS_STANDARD>().ToList()))
            {
                //this.gvPresc.MoveLast();
                this.layoutView1.MoveLast();
                throw new MessageException("请先保存当前处方");
            }
            // 默认选择第一个药局
            List<DeptDict> storages = this.lueDispensary.Properties.DataSource as List<DeptDict>;
            if(string.IsNullOrEmpty(this.lueDispensary.EditValue.ToString()))
                this.lueDispensary.EditValue = storages.Count > 0 ? storages[0].DEPT_CODE : "";
            //List<OUTP_ORDERS_STANDARD> prescList = this.bsPrescList.List.Cast<OUTP_ORDERS_STANDARD>().ToList();
            //if(prescList.Count(c => c.APPOINT_NO.Equals("新方")) > 0)
            //{
            //    throw new MessageException("有未保存的处方，请先保存");
            //}
            OUTP_ORDERS_STANDARD order = this.OrderBusiness.New();
            order = this.PrescBusiness.NewPresc(order, this.lueDispensary.EditValue?.ToString());
            this.bsPrescList.Add(order);
            this.bsPrescList.ResetBindings(false);
            DevGridViewHelper.GridViewLocateLastRow(gvPrescList, gvPrescList.RowCount - 1);
        }

        public void OrderCopy(List<OUTP_ORDERS_STANDARD> selectOrders)
        {
            List<OUTP_ORDERS_STANDARD> orderPresc = selectOrders.Where(o => OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE.Equals(o.ORDER_CLASS)).ToList();
            if (orderPresc.Count > 0)
            {
                OUTP_ORDERS_STANDARD presc = this.GetEditModel<OUTP_ORDERS_STANDARD>(this.bsPrescList);
                // 修复：如果没有处方或处方状态不是新建状态，则创建新方
                if(presc == null || !Constants.NEW_ORDER_STATE_STR.Equals(presc.STATE))
                {
                    this.NewPresc();
                }

                OUTP_ORDERS_STANDARD currentOrder = this.GetEditModel<OUTP_ORDERS_STANDARD>(bsPresc);
                List<OUTP_ORDERS_STANDARD> addOrders = this.OrderBusiness.OrderCopy(orderPresc);
                if (currentOrder != null)
                {
                    if (Constants.NEW_ORDER_STATE_STR.Equals(currentOrder.STATE) && string.IsNullOrEmpty(currentOrder.ORDER_TEXT))
                    {
                        this.OrderBusiness.Remove(currentOrder);
                        bsPresc.Remove(bsPresc.Current);
                    }
                }
                
                addOrders.ForEach(order =>
                {
                    this.PrescBusiness.SetCDrugProperty(this.GetEditModel<OUTP_ORDERS_STANDARD>(this.bsPrescList), order);
                    this.bsPresc.Add(order);
                    this.Orders.Add(order);
                    this.bsPresc.ResetBindings(false);

                    //this.Orders.Add(order);
                });
                bsPresc.ResetBindings(false);
            }
        }

        public override void Save()
        {
            SetViewDatatoSource();
            if (null == this.CurrentPresc)
            {
                return;
            }
            //List<OUTP_ORDERS_STANDARD> addOrders = this.GetSaveCurrentPresc(this.CurrentPresc.APPOINT_NO);//最开始版本
            List<OUTP_ORDERS_STANDARD> addOrders = this.bsPresc.List.Cast<OUTP_ORDERS_STANDARD>().ToList();//lh修改版本 没过滤非新开
            addOrders = addOrders.Where(item => item.APPOINT_NO.ToString("").Equals(this.CurrentPresc.APPOINT_NO) && Constants.NEW_ORDER_STATE_STR.Equals(item.STATE)).ToList();
            //List<OUTP_ORDERS_STANDARD> addOrders = this.Orders.Where(item => item.APPOINT_NO.ToString("").Equals(this.CurrentPresc.APPOINT_NO)
            //&& OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE.Equals(item.ORDER_CLASS) && (Constants.NEW_ORDER_STATE_STR.Equals(item.STATE) || item.STATE.Equals("保存"))).ToList();

            addOrders.ForEach(order =>
            {
                this.CurrentPresc.DECOCTION_ITEM_CODE = this.CurrentPresc.DECOCTION;
                order.DECOCTION_ITEM_CODE = order.DECOCTION;
                if (TxtPrescAttr.EditValue != null && !string.IsNullOrEmpty(TxtPrescAttr.EditValue.ToString()))
                {
                    order.PRESC_ATTR = TxtPrescAttr.EditValue.ToString();
                }
                if (luePespDise.EditValue != null && !string.IsNullOrEmpty(luePespDise.EditValue.ToString()))
                {
                    order.OPSP_DISE_CODE = luePespDise.EditValue.ToString();
                }
                if ((order.PRESC_ATTR == "慢特处方") && string.IsNullOrEmpty(order.OPSP_DISE_CODE))
                    throw new MessageException("请选择慢特病种！");
                this.PrescBusiness.SetCDrugProperty(this.CurrentPresc, order);
            });

            // 日志：记录保存开始
            WriteDebugLog("[CHECKPOINT] [UcCdrugPresc.Save] 开始保存 - 中药保存按钮被点击");

            if (addOrders != null && addOrders.Count > 0)
            {
                // 日志：记录待保存的医嘱信息
                WriteDebugLog($"[BATCH] [UcCdrugPresc.Save] 待保存医嘱 - 医嘱数量: {addOrders.Count}");
                for (int i = 0; i < addOrders.Count; i++)
                {
                    var order = addOrders[i];
                    WriteDebugLog($"[BATCH] [UcCdrugPresc.Save] 待保存医嘱[{i + 1}] - CLINIC_NO: {order.CLINIC_NO}, ORDER_NO: {order.ORDER_NO}, ORDER_SUB_NO: {order.ORDER_SUB_NO}, ITEM_NO: {order.ITEM_NO}, ORDER_TEXT: {order.ORDER_TEXT}, ORDER_CLASS: {order.ORDER_CLASS}, STATE: {order.STATE}");
                }

                try
                {
                    bool saveResult = this.OrderBusiness.Save(addOrders);

                    // 日志：记录保存结果
                    WriteDebugLog($"[CHECKPOINT] [OrderBusiness.Save] 数据库执行 - 执行结果: {saveResult}");

                    if (saveResult)
                    {
                        //打印
                        if (ClinicParameter.SaveAutoPrint == "1")
                        {
                            //PrintCDrugPrescription(addOrders);
                            //PrintCDrugPrescription_Changzhi(addOrders);
                        }
                        RefrushData();
                        TxtPrescAttr.ReadOnly = true;
                        this.SaveSuccess?.Invoke(this, null);

                        // 日志：记录保存成功
                        WriteDebugLog("[CHECKPOINT] [UcCdrugPresc.Save] 保存成功 - 中药保存成功");
                        MessageHelper.ShowSuccess("草药保存成功");
                    }
                    else
                    {
                        // 日志：记录保存失败
                        WriteDebugLog("[UcCdrugPresc.Save] 错误: 保存失败，OrderBusiness.Save返回false");
                    }
                }
                catch (Exception ex)
                {
                    // 日志：记录保存异常
                    WriteDebugLog($"[UcCdrugPresc.Save] 错误: 保存异常: {ex.Message}");
                    throw;
                }
            }
        }

        // 打印草药处方
        private void PrintCDrugPrescription(List<OUTP_ORDERS_STANDARD> addOrders)
        {
            Hashtable hasParam = new Hashtable();
            DataSet dsPrint;
            string AppCode = "OUTPDOCT";
            hasParam.Add("ORDER_CLASS", addOrders[0].ORDER_CLASS);
            hasParam.Add("CLINIC_NO", addOrders[0].CLINIC_NO);
            hasParam.Add("APPOINT_NO", addOrders[0].APPOINT_NO);
            hasParam.Add("OUTP_SERIAL_NO", addOrders[0].SERIAL_NO);
            if (!string.IsNullOrEmpty(addOrders[0].SERIAL_NO))
            {
                dsPrint = XtraReportHelper.GetPrintData_DataBase("门诊医生处方单_普通中药", hasParam, AppCode);

                XtraReportHelper.Print("门诊医生处方单_普通中药", dsPrint, false, AppCode);
            }
        }

        // 打印草药处方，长治
        private void PrintCDrugPrescription_Changzhi(List<OUTP_ORDERS_STANDARD> addOrders)
        {
            Hashtable hasParam = new Hashtable();
            DataSet dsPrint;
            string AppCode = "OUTPDOCT";
            hasParam.Add("ORDER_CLASS", addOrders[0].ORDER_CLASS);
            hasParam.Add("CLINIC_NO", addOrders[0].CLINIC_NO);
            hasParam.Add("APPOINT_NO", addOrders[0].APPOINT_NO);
            hasParam.Add("OUTP_SERIAL_NO", addOrders[0].SERIAL_NO);
            hasParam.Add("HIS_UNIT_CODE", SystemParm.HisUnitCode);
            if (!string.IsNullOrEmpty(addOrders[0].SERIAL_NO))
            {
                dsPrint = XtraReportHelper.GetPrintData_DataBase("门诊医生处方单_普通中药（长治）", hasParam, AppCode);

                XtraReportHelper.Print("门诊医生处方单_普通中药（长治）", dsPrint, false, AppCode);
            }
        }

        public override void RefrushData()
        {
            this.Orders = this.OrderBusiness.GetOrdersList();
            List<OUTP_ORDERS_COSTS_STANDARD> costs = this.OrderBusiness.GetOrderCostsList();
            this.OrderBusiness.BingOrdersCosts(Orders, costs);
            this.OrderBusiness.SetMaxOrderNo(this.Orders);

            // 修复：同步更新AddOrders集合，确保与数据库状态一致
            // 清空AddOrders集合，然后添加从数据库查询到的有效医嘱
            this.OrderBusiness.AddOrders.Clear();
            this.Orders.ForEach(order => {
                this.OrderBusiness.AddOrders.Add(order);
            });

            this.InitPrescList();

            // 关键修复：刷新药品字典数据，确保库存信息同步
            try
            {
                if (!string.IsNullOrEmpty(this.lueDispensary.EditValue?.ToString()))
                {
                    this.InitDrugDict();

                    // 记录药品字典刷新日志
                    string logPath = @"..\Client\LOG\exLOG\门诊医生站_药品刷新_" + DateTime.Now.ToString("yyyyMMdd") + ".log";
                    string logEntry = string.Format(
                        "[{0:yyyy-MM-dd HH:mm:ss}] [INFO] [药品刷新] 中药数据刷新完成，药房={1}, 药品数量={2}\r\n",
                        DateTime.Now, this.lueDispensary.EditValue?.ToString(), this.DrugDicts?.Count ?? 0
                    );
                    System.IO.File.AppendAllText(logPath, logEntry);
                }
            }
            catch (Exception ex)
            {
                // 记录药品字典刷新失败日志
                try
                {
                    string logPath = @"..\Client\LOG\exLOG\门诊医生站_界面异常_" + DateTime.Now.ToString("yyyyMMdd") + ".log";
                    string logEntry = string.Format(
                        "[{0:yyyy-MM-dd HH:mm:ss}] [ERROR] [药品刷新] 中药药品字典刷新失败: {1}\r\n",
                        DateTime.Now, ex.Message
                    );
                    System.IO.File.AppendAllText(logPath, logEntry);
                }
                catch { /* 忽略日志错误 */ }
            }

            DevGridViewHelper.GridViewLocateLastRow(gvPrescList, gvPrescList.RowCount - 1);
        }
        public override void DelItem()
        {
            OUTP_ORDERS_STANDARD order = this.GetEditModel<OUTP_ORDERS_STANDARD>(this.bsPresc);
            this.bsPresc.Remove(order);

            if (Orders == null)
            {
                return;
            }

            this.Orders.Remove(order);
            if (order != null && !order.STATE.Equals(Constants.NEW_ORDER_STATE_STR))
            {
                List<OUTP_ORDERS_STANDARD> delOrders = new List<OUTP_ORDERS_STANDARD> { order };
                if (this.OrderBusiness.Delete(delOrders))
                {
                    // 修复：删除成功后，同步更新AddOrders集合，确保与数据库状态一致
                    this.RefrushData();
                }
            }
            else
            {
                // 修复：对于未保存的医嘱，也需要从AddOrders集合中移除
                this.OrderBusiness.Remove(order);
            }
            this.bsPresc.ResetBindings(false);
        }

        public void DelPresc()
        {
            List<OUTP_ORDERS_STANDARD> delOrders = this.bsPresc.List.Cast<OUTP_ORDERS_STANDARD>().ToList();
            if (this.OrderBusiness.Delete(delOrders))
            {
                this.RefrushData();
            }
        }

        public override void Del()
        {
            List<OUTP_ORDERS_STANDARD> delOrders = this.bsPresc.List.Cast<OUTP_ORDERS_STANDARD>().ToList();
            if (this.OrderBusiness.Delete(delOrders))
            {
                this.RefrushData();
            }
        }

        public override void ResetData()
        {
            base.ResetData();
            this.bsPresc.Clear();
            this.bsPrescList.Clear();
            this.InitPrescModel();
            this.InitStorages();
            this.InitUsageDesc();
        }
        public void PrintPresc()
        {

        }
        private IOrders OrderBusiness { get; set; }
        private ICPresc PrescBusiness { get; set; }
        public IOutpMr OutpMrBusiness { get; set; }
        private OutpPatientInfo CurrentPatientInfo { get; set; }
        private List<OUTP_ORDERS_STANDARD> Orders { get; set; }
        private BindingSource bsPrescList { get; set; }
        private BindingSource bsPresc { get; set; }
        private BindingSource bsTreatMaster { get; set; }
        private BindingSource bsDrugDict { get; set; }
        private List<V_CDRUG_DICT> DrugDicts { get; set; }
        private List<CDRUG_PROJECT_MASTER> CDrugMaster { get; set; }
        private OUTP_ORDERS_STANDARD CurrentPresc { get; set; }

        private DiagnosisInputSrv diagnosisInputSrv { get; set; }
        private OutpMr MrInfo { get; set; }
        private void SetPrescAppendPropertyEditable(bool readOnly)
        {
            this.lueUsageDesc.ReadOnly = readOnly;
            this.lueDecoction.ReadOnly = readOnly;
            this.LuDiagDesc.ReadOnly = readOnly;
            this.teRepetition.ReadOnly = readOnly;
            this.txtPrescName.ReadOnly = readOnly;
            this.txtRulesOfTreatment.ReadOnly = readOnly;
            this.lueFreq.ReadOnly = readOnly;
            this.teTaboo.ReadOnly = readOnly;
            this.speHERBAL_EXTRACT_JUICE_QUANTITY.ReadOnly = readOnly;
            this.speHERBAL_DOSAGE.ReadOnly = readOnly;
        }
        private void SetStorageEditable(bool editable)
        {
            this.lueDispensary.ReadOnly = editable;

        }
        private void SetDiagnosis()
        {
            this.LuDiagDesc.Properties.DataSource = diagnosisInputSrv.GetOutpDiagnosis(this.CurrentPatientInfo.VISIT_DATE, this.CurrentPatientInfo.VISIT_NO, this.MrInfo.ORDINAL);
        }
        public void ChangeMrAndDiag()
        {
            this.MrInfo = OrderBusiness.GetMrInfo();
            this.LuDiagDesc.Properties.DataSource = OrderBusiness.GetDtDiagnosis(); ;
        }
        private void InitPrescList()
        {
            List<OUTP_ORDERS_STANDARD> prescOrders = this.Orders.Where(o => OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE.Equals(o.ORDER_CLASS)).ToList();
            this.bsPresc.Clear();
            prescOrders.ForEach(order =>
            {
                this.bsPresc.Add(order);
            });

            if (this.bsPresc.Count > 0)
            {
                SetStorageEditable(true);
            }
            else
            {
                SetStorageEditable(false);
            }
            List<OUTP_ORDERS_STANDARD> prescList = this.PrescBusiness.GetPrescList(this.Orders);
            this.bsPrescList.Clear();
            prescList.ForEach(order =>
            {
                this.bsPrescList.Add(order);
            });
            UnBindControllEvent();
            this.bsPrescList.ResetBindings(false);
            BingControllEvent();
            // 默认选择第一个药局
            List<DeptDict> storages = this.lueDispensary.Properties.DataSource as List<DeptDict>;
            if (string.IsNullOrEmpty(this.lueDispensary.EditValue.ToString()))
                this.lueDispensary.EditValue = storages.Count > 0 ? storages[0].DEPT_CODE : "";
            if (string.IsNullOrWhiteSpace(this.TxtPrescAttr.EditValue?.ToString()))
            {
                this.TxtPrescAttr.EditValue = PrescParameter.PrescAttr;
            }
        }
        private void BindPrescProperty()
        {
            this.BindControl(this.lueFreq, bsPrescList, "EditValue", "FREQUENCY");
            this.BindControl(this.teRepetition, bsPrescList, "EditValue", "REPETITION");
            this.BindControl(this.speHERBAL_DOSAGE, bsPrescList, "EditValue", "HERBAL_DOSAGE");
            this.BindControl(this.speHERBAL_EXTRACT_JUICE_QUANTITY, bsPrescList, "EditValue", "HERBAL_EXTRACT_JUICE_QUANTITY");
            this.BindControl(this.lueUsageDesc, bsPrescList, "EditValue", "USAGE_DESC");
            this.BindControl(this.LuDiagDesc, bsPrescList, "EditValue", "DIAGNOSIS_DESC");
            this.BindControl(this.txtRulesOfTreatment, bsPrescList, "EditValue", "HERBAL_RULES_OF_TREATMENT");
            this.BindControl(this.lueDispensary, bsPrescList, "EditValue", "PERFORMED_BY");
            //this.BindControl(this.lueDecoction, bsPrescList, "EditValue", "DECOCTION_ITEM_CODE");
            this.BindControl(this.lueDecoction, bsPrescList, "EditValue", "DECOCTION");
            this.BindControl(this.teTaboo, bsPrescList, "EditValue", "PRESC_COMM_TABOO");
            this.BindControl(this.txtPrescName, bsPrescList, "EditValue", "CPRESC_NAME");
        }
        private void BindPrescProperty(OUTP_ORDERS_STANDARD order)
        {
            UnBindControllEvent();
            CurrentPresc = order;
            //this.BindControl(this.lueAdmin, "EditValue", order, "ADMINISTRATION");
            //this.BindControl(this.lueFreq, "EditValue", order, "FREQUENCY");
            //this.BindControl(this.speHERBAL_DOSAGE, "EditValue", order, "HERBAL_DOSAGE");
            //this.BindControl(this.speHERBAL_EXTRACT_JUICE_QUANTITY, "EditValue", order, "HERBAL_EXTRACT_JUICE_QUANTITY");
            //this.BindControl(this.lueUsageDesc, "EditValue", order, "USAGE_DESC");
            //this.BindControl(this.LuDiagDesc, "EditValue", order, "DIAGNOSIS_DESC");
            //this.BindControl(this.txtRulesOfTreatment, "EditValue", order, "HERBAL_RULES_OF_TREATMENT");
            //this.BindControl(this.lueDispensary, "EditValue", order, "PERFORMED_BY");
            //this.BindControl(this.lueDecoction, "EditValue", order, "DECOCTION_ITEM_CODE");
            //this.BindControl(this.txtPrescName, "EditValue", order, "CPRESC_NAME");
            //this.BindControl(this.teRepetition, "EditValue", order, "REPETITION");
            //this.BindControl(this.teTaboo, "EditValue", order, "FREQ_DETAIL");
            this.BindCurrentPresc(CurrentPresc.APPOINT_NO);
            bool canEdit = CurrentPresc.CHARGE_INDICATOR.ToInt(0) == 0;
            if (canEdit)
            {
                if (string.IsNullOrEmpty(CurrentPresc.DIAGNOSIS_DESC))
                {
                    CurrentPresc.DIAGNOSIS_DESC = this.MrInfo.DIAG_DESC;
                }
                if (this.bsPresc.Count > 0)
                {
                    SetStorageEditable(true);
                }
                else
                {
                    SetStorageEditable(false);
                }
            }
            this.TxtPrescAttr.EditValue = CurrentPresc.PRESC_ATTR ?? PrescParameter.PrescAttr;
            this.luePespDise.EditValue = CurrentPresc.OPSP_DISE_CODE;
            SetPrescAppendPropertyEditable(!canEdit);

            BingControllEvent();
        }
        /// <summary>
        /// 解绑控件事件
        /// </summary>
        void UnBindControllEvent()
        {
            lueDispensary.EditValueChanged -= lueDispensary_EditValueChanged;
            lueUsageDesc.EditValueChanged -= lueUsageDesc_EditValueChanged;
            lueDecoction.EditValueChanged -= lueDecoction_EditValueChanged;
            teRepetition.EditValueChanged -= teRepetition_EditValueChanged;
        }

        private void teRepetition_EditValueChanged(object sender, EventArgs e)
        {
            this.ExcuteExcetionMethodChangeCursor(() =>
            {
                if (this.CurrentPresc == null)
                {
                    return;
                }

                // 修复：确保只修改当前处方的剂数，避免影响其他处方
                // 获取当前处方的剂数值
                int currentRepetition = this.CurrentPresc.REPETITION.ToInt(1);

                // 只获取当前处方下的药品列表，避免影响其他处方
                List<OUTP_ORDERS_STANDARD> currentPrescDrugs = this.bsPresc.List.Cast<OUTP_ORDERS_STANDARD>()
                    .Where(order => order.APPOINT_NO.ToString("").Equals(this.CurrentPresc.APPOINT_NO))
                    .ToList();

                // 创建当前处方的副本，避免直接修改原对象影响其他处方
                OUTP_ORDERS_STANDARD currentPrescCopy = new OUTP_ORDERS_STANDARD();
                // 复制关键属性
                currentPrescCopy.APPOINT_NO = this.CurrentPresc.APPOINT_NO;
                currentPrescCopy.REPETITION = currentRepetition;
                currentPrescCopy.ORDER_CLASS = this.CurrentPresc.ORDER_CLASS;
                currentPrescCopy.STATE = this.CurrentPresc.STATE;

                this.PrescBusiness.CellValueChanged("REPETITION", currentPrescCopy, currentPrescDrugs);

                string strMsg = "";
                if (CurrentPresc.STATE == "录入")
                {
                    foreach (OUTP_ORDERS_STANDARD order in currentPrescDrugs)
                    {
                        if(order.STATE=="录入")
                        {
                            if(!CalculateYJDX(ref strMsg, order)&&!string.IsNullOrEmpty(strMsg))
                            {
                                XtraMessageBox.Show(strMsg, "提示");
                            }
                        }
                    }
                }
            });
        }

        private void lueDecoction_EditValueChanged(object sender, EventArgs e)
        {
            if(this.CurrentPresc==null)
            {
                return;
            }
            this.ExcuteExcetionMethodChangeCursor(() =>
            {
                List<OUTP_ORDERS_STANDARD> prescs = this.GetEditCurrentPresc(this.CurrentPresc.APPOINT_NO);
                prescs.ForEach(order =>
                {
                    CurrentPresc.DECOCTION_ITEM_CODE = CurrentPresc.DECOCTION;
                    order.DECOCTION_ITEM_CODE = CurrentPresc.DECOCTION;
                    order.DECOCTION_ITEM_CODE = CurrentPresc.DECOCTION_ITEM_CODE;
                });
            });
        }

        private void lueUsageDesc_EditValueChanged(object sender, EventArgs e)
        {
            this.ExcuteExcetionMethodChangeCursor(() =>
            {
                DataRowView drv = lueUsageDesc.GetSelectedDataRow() as DataRowView;
                if (drv == null)
                    return;
                string s = drv.Row["USAGE_DESC"].ToString("");
                string administration = drv.Row["ADMINISTRATION"].ToString();
                string frequency = drv.Row["FREQUENCY"].ToString();
                if (!string.IsNullOrEmpty(administration))
                {
                    //this.lueAdmin.EditValue = administration;
                }
                if (!string.IsNullOrEmpty(frequency))
                {
                    this.lueFreq.EditValue = frequency;
                }
            });
        }


        private List<OUTP_ORDERS_STANDARD> GetEditCurrentPresc(string aPPOINT_NO)
        {
            List<OUTP_ORDERS_STANDARD> orders = this.Orders.Where(item => item.APPOINT_NO.ToString("").Equals(aPPOINT_NO)
            && OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE.Equals(item.ORDER_CLASS)).ToList();
            return orders;
        }

        private List<OUTP_ORDERS_STANDARD> GetSaveCurrentPresc(string aPPOINT_NO)
        {
            List<OUTP_ORDERS_STANDARD> orders = this.Orders.Where(item => item.APPOINT_NO.ToString("").Equals(aPPOINT_NO)
            && OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE.Equals(item.ORDER_CLASS) && (Constants.NEW_ORDER_STATE_STR.Equals(item.STATE) || item.STATE.Equals("保存"))).ToList();
            foreach (OUTP_ORDERS_STANDARD item in orders)
            {
                if (item.STATE.Equals(Constants.NEW_ORDER_STATE_STR))
                {
                    if (TxtPrescAttr.EditValue != null && !string.IsNullOrEmpty(TxtPrescAttr.EditValue.ToString()))
                    {
                        item.PRESC_ATTR = TxtPrescAttr.EditValue.ToString();
                    }
                    if (luePespDise.EditValue != null && !string.IsNullOrEmpty(luePespDise.EditValue.ToString()))
                    {
                        item.OPSP_DISE_CODE = luePespDise.EditValue.ToString();
                    }
                    if ((item.PRESC_ATTR == "慢特处方") && string.IsNullOrEmpty(item.OPSP_DISE_CODE))
                        throw new MessageException("请选择慢特病种！");
                }
            }
            return orders;
        }

        private void lueFreq_EditValueChanged(object sender, EventArgs e)
        {
            this.ExcuteExcetionMethodChangeCursor(() =>
            {
                List<OUTP_ORDERS_STANDARD> prescs = this.GetEditCurrentPresc(this.CurrentPresc.APPOINT_NO);

                this.PrescBusiness.CellValueChanged("FREQUENCY", this.CurrentPresc, prescs);
                //prescs.ForEach(order =>
                //{
                //    order.FREQUENCY = CurrentPresc.FREQUENCY;
                //});
            });
        }

        private void lueDispensary_EditValueChanged(object sender, EventArgs e)
        {
            // 默认选择第一个药局
            List<DeptDict> storages = this.lueDispensary.Properties.DataSource as List<DeptDict>;
            if(this.lueDispensary.EditValue ==null)
                this.lueDispensary.EditValue = storages.Count > 0 ? storages[0].DEPT_CODE : "";
            this.ExcuteExcetionMethodChangeCursor(() =>
            {
                this.InitDrugDict();
            });
        }

        /// <summary>
        /// 绑定控件事件
        /// </summary>
        void BingControllEvent()
        {
            lueDispensary.EditValueChanged += lueDispensary_EditValueChanged;
            lueUsageDesc.EditValueChanged += lueUsageDesc_EditValueChanged;
            lueDecoction.EditValueChanged += lueDecoction_EditValueChanged;
            teRepetition.EditValueChanged += teRepetition_EditValueChanged;
        }
        /// <summary>
        /// 绑定当前编辑的处方，prescNo是appointNo
        /// </summary>
        /// <param name="prescNo"></param>
        private void BindCurrentPresc(string prescNo)
        {
            this.bsPresc.Clear();
            this.Orders.ForEach(order =>
            {
                if (OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE.Equals(order.ORDER_CLASS) &&
                prescNo.Equals(order.APPOINT_NO))
                {
                    this.bsPresc.Add(order);
                }
            });
            this.bsPresc.ResetBindings(false);
        }

        private void initGridView()
        {
            this.InitPrescModel();
            this.InitDrugDict();
        }

        private void InitDrugDict()
        {
            DrugDicts = this.PrescBusiness.GetCDrugDicts(this.lueDispensary.EditValue.ToString());
            this.bsDrugDict.Clear();
            DrugDicts?.ForEach(drugDict =>
            {
                this.bsDrugDict.Add(drugDict);
            });
            this.bsDrugDict.ResetBindings(false);
        }

        private void InitPrescModel()
        {
            CDrugMaster = this.PrescBusiness.GetCdrugProjectMaster(GlobalValue.DeptCode, SystemParm.LoginUser.USER_NAME, SystemParm.HisUnitCode);
            bsTreatMaster.Clear();
            CDrugMaster?.ForEach(master =>
            {
                this.bsTreatMaster.Add(master);
            });
            this.bsTreatMaster.ResetBindings(false);
        }

        private void InitLookUpEdit()
        {
            DataTable dtAdministration = new DataTable();
            DataRow[] drs = CommonDict.DtAdministrationDict.Select("CDRUG_USING_FLAG = '1'");
            if(drs!=null&&drs.Length>0)
                dtAdministration=drs.CopyToDataTable();

            this.lueFreq.Properties.DataSource = CommonDict.DtPerformFreqDict.Copy();
            InitStorages();
            this.lueDecoction.Properties.DataSource = this.PrescBusiness.GetDecoctionVsChargeDict();
            InitUsageDesc();
            this.lue_Administration.DataSource = this.PrescBusiness.GetDecoctionDict();
            this.lue_SpecialRequest.DataSource = this.PrescBusiness.GetSpecialRequestDict();

            TxtPrescAttr.Properties.DataSource = CommonDict.DtPrescAttr;
            TxtPrescAttr.Properties.DisplayMember = "PRESC_ATTR_NAME";
            TxtPrescAttr.Properties.ValueMember = "PRESC_ATTR_NAME";
        }

        private void InitUsageDesc()
        {
            this.lueUsageDesc.Properties.DataSource = this.PrescBusiness.GetUsageDesc();
        }

        private void InitStorages()
        {
            List<DeptDict> storages = this.PrescBusiness.GetDrugStore();
            this.lueDispensary.Properties.DataSource = storages;
            this.lueDispensary.EditValue = storages.Count > 0 ? storages[0].DEPT_CODE : "";
        }

        protected override void MyDispose()
        {
            bsPrescList.Dispose();
            bsPresc.Dispose();
            bsTreatMaster.Dispose();
            bsDrugDict.Dispose();
            this.OrderBusiness = null;
            this.PrescBusiness = null;
            base.MyDispose();
        }

        public UcCdrugPresc()
        {
            InitializeComponent();
            bsPrescList = new BindingSource();
            bsPrescList.DataSource = typeof(OUTP_ORDERS_STANDARD);
            bsPresc = new BindingSource();
            bsPresc.DataSource = typeof(OUTP_ORDERS_STANDARD);
            bsDrugDict = new BindingSource();
            bsDrugDict.DataSource = typeof(V_CDRUG_DICT);
            bsTreatMaster = new BindingSource();
            bsTreatMaster.DataSource = typeof(CDRUG_PROJECT_MASTER);
            this.GCModel.DataSource = this.bsTreatMaster;
            this.gcDrug.DataSource = this.bsDrugDict;
            this.gcPresc.DataSource = this.bsPresc;
            this.gcPrescList.DataSource = this.bsPrescList;
            this.BindPrescProperty();
        }

        private void rgSelect_SelectedIndexChanged(object sender, EventArgs e)
        {
            this.SetInputAreaControlProperties(rgSelect.EditValue.ToString("0") == "0" ? true : false);
            this.keyBoard1.ClearInputTxt();
            this.keyBoard1.InvokeBoardInput();
        }
        /// <summary>
        /// 设置选择模板与药品是控件的显示
        /// </summary>
        /// <param name="isModel"></param>
        private void SetInputAreaControlProperties(bool isModel = true)
        {
            if (isModel)
            {
                this.gcDrug.Visible = false;
                this.GCModel.Visible = true;
            }
            else
            {
                this.gcDrug.Visible = true;
                this.GCModel.Visible = false;
            }
        }
        #region 诊断新增
        private void SaveOutMr()
        {
            this.OutpMrBusiness.SaveOrUpdate(this.MrInfo);
        }
        private void LuDiagDesc_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            if (!"新增诊断".Equals(e.Button.Caption))
            {
                return;
            }
            this.ExcuteExcetionMethodChangeCursor(() =>
            {
                if (this.CurrentPatientInfo == null)
                {
                    throw new MessageException("请选择一个病人");
                }
                FrmDiagnosisInput frmDiags = new FrmDiagnosisInput();
                frmDiags.CurrentOutpPatientInfo = this.CurrentPatientInfo;
                frmDiags.OutpMr = this.MrInfo;
                frmDiags.StartPosition = FormStartPosition.CenterScreen;
                frmDiags.ShowDialog();
                if (frmDiags.DialogResult == DialogResult.OK)
                {
                    this.SetDiagnosis();
                    this.SaveOutMr();

                    if (OtherParameter.ViewVersion == "1" || OtherParameter.ViewVersion == "2")
                    {
                        FrmOutpPatientList frm = ((FrmOrderCPresc)this.ParentForm).uOrders.ParentForm as FrmOutpPatientList;
                        if (frm != null)
                        {
                            frm.uom.OnDiagnosisChanged();
                        }
                    }
                    else if (OtherParameter.ViewVersion == "3")
                    {
                        ((FrmOutpPatientList)this.ParentForm).uom.OnDiagnosisChanged();
                    }
                }
            });
        }
        #endregion

        private void gvPrescList_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            OUTP_ORDERS_STANDARD order = this.GetEditModel<OUTP_ORDERS_STANDARD>(this.bsPrescList);
            if (null == order)
            {
                return;
            }
            PrescBusiness.SetDecoctionItemCode(this.Orders, order);
            List<OUTP_ORDERS_STANDARD> currentPersc = this.Orders.Where(item => item.APPOINT_NO.ToString("").Equals(order.APPOINT_NO)
            && OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE.Equals(item.ORDER_CLASS)).ToList();
            //this.bsPresc.Filter = $"APPOINT_NO = '{order.APPOINT_NO}' AND ROWSTATE >= -1";
            this.bsPresc.Clear();
            currentPersc.ForEach(presc =>
            {
                this.bsPresc.Add(presc);
            });
            this.bsPresc.ResetBindings(false);
            BindPrescProperty(order);
            setProperty(order.STATE!="录入");
        }

        private void setProperty(bool isRead)
        {
            lueDispensary.ReadOnly = isRead;
            lueDecoction.ReadOnly = isRead;
            LuDiagDesc.ReadOnly = isRead;
            txtRulesOfTreatment.ReadOnly = isRead;
            lueUsageDesc.ReadOnly = isRead;
            lueFreq.ReadOnly = isRead;
            teRepetition.ReadOnly = isRead;
            txtPrescName.ReadOnly = isRead;
            speHERBAL_EXTRACT_JUICE_QUANTITY.ReadOnly = isRead;
            speHERBAL_DOSAGE.ReadOnly = isRead;
            teTaboo.ReadOnly = isRead;
            TxtPrescAttr.ReadOnly = isRead;
            luePespDise.ReadOnly = isRead;
        }

        private void gvDrug_DoubleClick(object sender, EventArgs e)
        {
            this.ExcuteExcetionMethodChangeCursor(() =>
            {
                string dispensary = this.lueDispensary.EditValue?.ToString();
                if (string.IsNullOrEmpty(dispensary))
                {
                    throw new MessageException("请先选择药局");
                }
                OUTP_ORDERS_STANDARD prescList = this.bsPrescList.Current as OUTP_ORDERS_STANDARD;

                if (prescList != null && prescList.STATE!="录入")
                {
                    throw new MessageException("已保存处方不允许加药");
                }

                if (prescList == null || Constants.CHARGE_INDICATOR_VALUE.Equals(prescList.STATE))
                {
                    this.NewPresc();
                    prescList = this.bsPrescList.Current as OUTP_ORDERS_STANDARD;
                }
                V_CDRUG_DICT drugDict = this.GetEditModel<V_CDRUG_DICT>(this.bsDrugDict);
                InputResult result = PrescBusiness.CreateInputResult(drugDict);

                // 修复：使用OrderBusiness.Add()方法确保正确的ORDER_NO分配
                OUTP_ORDERS_STANDARD order = this.OrderBusiness.Add();
                order.PERFORMED_BY = dispensary;
                // 注意：ORDER_NO和ORDER_SUB_NO已经在Add()方法中正确设置，不需要重新设置

                // 修复：使用当前有效的医嘱列表进行重复检查，而不是全部的bsPresc
                // 只包含状态为"录入"的医嘱，排除已删除或毁方的医嘱
                List<OUTP_ORDERS_STANDARD> addOrders = this.bsPresc.List.Cast<OUTP_ORDERS_STANDARD>().Where(o => Constants.NEW_ORDER_STATE_STR.Equals(o.STATE)).ToList();
                string strMsg = "";
                if (this.OrderBusiness.SetClinciItem(order, addOrders, result,ref strMsg))
                {
                    this.PrescBusiness.SetCDrugProperty(this.GetEditModel<OUTP_ORDERS_STANDARD>(this.bsPrescList), order);
                    this.bsPresc.Add(order);
                    this.Orders.Add(order);
                    if(!CalculateYJDX(ref strMsg)&&!string.IsNullOrEmpty(strMsg))
                        throw new MessageException(strMsg);
                    this.bsPresc.ResetBindings(false);
                    this.SetStorageEditable(true);
                    this.keyBoard1.ClearInputTxt();
                }
                else
                {
                    if (!string.IsNullOrEmpty(strMsg))
                    {
                        throw new MessageException(strMsg);
                    }
                }

            });
        }
        private bool SetViewDatatoSource()
        {
            //this.gvPresc.CloseEditor();
            this.layoutView1.CloseEditor();
            //bool updateDataSourceState = this.gvPresc.UpdateCurrentRow();
            bool updateDataSourceState = this.layoutView1.UpdateCurrentRow();
            if (!updateDataSourceState)
            {
                XtraMessageBox.Show("更新数据源失败，请重试");
            }
            return updateDataSourceState;
        }



        private void gvPresc_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            string columnName = e.Column.FieldName;
            this.ExcuteExcetionMethodChangeCursor(() =>
            {
                this.PrescBusiness.CellValueChanged(columnName, this.GetEditModel<OUTP_ORDERS_STANDARD>(this.bsPresc), this.bsPresc.List.Cast<OUTP_ORDERS_STANDARD>().ToList());
                this.bsPresc.ResetBindings(false);
            });
        }

        private void gvTreatMaster_DoubleClick(object sender, EventArgs e)
        {
            this.ExcuteExcetionMethodChangeCursor(() =>
            {
                string dispensary = this.lueDispensary.EditValue?.ToString();
                if (string.IsNullOrEmpty(dispensary))
                {
                    throw new MessageException("请先选择药局");
                }
                CDRUG_PROJECT_MASTER master = this.bsTreatMaster.Current as CDRUG_PROJECT_MASTER;
                OUTP_ORDERS_STANDARD prescList = this.bsPrescList.Current as OUTP_ORDERS_STANDARD;
                if (master == null|| prescList == null)
                {
                    return;
                }
                if (prescList != null && prescList.STATE!="录入")
                {
                    throw new MessageException("已开处方不允许加药");
                }
                //if (prescList == null || Constants.CHARGE_INDICATOR_VALUE.Equals(prescList.STATE))
                //{
                //    this.NewPresc();
                //    prescList = this.bsPrescList.Current as OUTP_ORDERS_STANDARD;
                //}
                if (!string.IsNullOrEmpty(master.ADMINISTRATION))
                {
                    prescList.ADMINISTRATION = master.ADMINISTRATION;
                }
                if (!string.IsNullOrEmpty(master.FREQUENCY))
                {
                    prescList.FREQUENCY = master.FREQUENCY;
                }
                prescList.REPETITION = master.REPETITION.ToInt(1);
                prescList.USAGE_DESC = master.USAGE_DESC;
                this.bsPrescList.ResetCurrentItem();
                master.ITEMS?.ForEach(item =>
                {
                    string strMsg = "";
                    InputResult result = PrescBusiness.CreateInputResult(item, dispensary);
                    if (null == result)
                    {
                        return;
                    }
                    // 修复：使用OrderBusiness.Add()方法确保正确的ORDER_NO分配
                    OUTP_ORDERS_STANDARD order = this.OrderBusiness.Add();
                    // 修复：使用当前有效的医嘱列表进行重复检查，而不是全部的bsPresc
                    // 只包含状态为"录入"的医嘱，排除已删除或毁方的医嘱
                    List<OUTP_ORDERS_STANDARD> addOrders = this.bsPresc.List.Cast<OUTP_ORDERS_STANDARD>().Where(o => Constants.NEW_ORDER_STATE_STR.Equals(o.STATE)).ToList();
                    if (this.OrderBusiness.SetClinciItem(order, addOrders, result,ref strMsg))
                    {
                        this.PrescBusiness.SetCDrugProperty(this.GetEditModel<OUTP_ORDERS_STANDARD>(this.bsPrescList), order);
                        // 注意：ORDER_NO和ORDER_SUB_NO已经在Add()方法中正确设置，不需要重新设置
                        this.bsPresc.Add(order);
                        this.Orders.Add(order);
                        if (!CalculateYJDX(ref strMsg,order) && !string.IsNullOrEmpty(strMsg))
                        {
                            XtraMessageBox.Show(strMsg, "提示");
                        }
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(strMsg))
                        {
                            XtraMessageBox.Show(strMsg, "提示");
                        }
                    }
                });
                this.bsPresc.ResetBindings(false);
                this.SetStorageEditable(false);
            });

        }

        private void gvPresc_Click(object sender, EventArgs e)
        {
            //DevGridViewHelper.ClickGridCheckBox(this.gvPresc, "INSUR_ADULT", "", "STATE", Constants.NEW_ORDER_STATE_STR);
            this.bsPresc.ResetBindings(false);
        }

        private void keyBoard1_FilterResult(string result)
        {
            result = result.ToUpper();
            string filterStr = string.Empty;
            if (this.rgSelect.EditValue.ToString("0") == "1") //药品
            {
                //if (!string.IsNullOrEmpty(lueDispensary.EditValue?.ToString()))
                //    filterStr = " STORAGE = '" + lueDispensary.EditValue?.ToString() + "' ";
                //if (!string.IsNullOrEmpty(result))
                //{
                //    if (filterStr.Length > 0) filterStr += " AND ";
                //    filterStr += " ( INPUT_CODE like '%" + result + "%' or DRUG_CODE like '%" + result + "%' or DRUG_NAME like '%" + result + "%' ) ";
                //}
                List<V_CDRUG_DICT> drugDicts = this.DrugDicts.Where(drug => drug.INPUT_CODE.Contains(result) || drug.DRUG_CODE.Contains(result) || drug.DRUG_NAME.Contains(result)).ToList();
                this.bsDrugDict.Clear();
                drugDicts.ForEach(drug =>
                {
                    this.bsDrugDict.Add(drug);
                });
                //this.dtDrugDict.DefaultView.RowFilter = filterStr;
                //this.gvDrug.BeginUpdate();
                //this.bsDrugDict.Filter = filterStr;
                //this.gvDrug.EndUpdate();
            }
            else //模板
            {
                //if (!string.IsNullOrEmpty(result))
                //    filterStr = " ( TITLE like '%" + result + "%' ) ";
                this.bsTreatMaster.Clear();
                this.CDrugMaster.Where(master => master.TITLE.Contains(result) || master.INPUT_CODE.ToString("").Contains(result)).ToList().ForEach(f =>
                {
                    this.bsTreatMaster.Add(f);
                });
                //this.bsTreatMaster.Filter = filterStr;
                //this.dtPjMaster.DefaultView.RowFilter = filterStr;
            }
        }

        private void RcINSURANCE_FLAG_CheckStateChanged(object sender, EventArgs e)
        {
            OUTP_ORDERS_STANDARD drugItem = this.GetEditModel<OUTP_ORDERS_STANDARD>(bsPresc);
            if ((sender as CheckEdit).Checked)
            {
                drugItem.INSURANCE_FLAG = "1";
            }
            else
            {
                drugItem.INSURANCE_FLAG = "0";
            }
        }


        public override void Print()
        {
            
            
            try
            {
                FrmOutpPrint frm = new FrmOutpPrint();
                frm.StartPosition = FormStartPosition.CenterScreen;
                frm.CurrentPatientInfo = this.CurrentPatientInfo;
                frm.DeptCode = GlobalValue.DeptCode;
                frm.AppCode = GlobalValue.AppCode;
                frm.ShowDialog();
                //List<OUTP_ORDERS_STANDARD> listOrder = bsPresc.List.Cast<OUTP_ORDERS_STANDARD>().ToList();
                //if (listOrder == null || listOrder.Count < 1)
                //    return;
                ////打印
                //Hashtable hasParam = new Hashtable();
                //DataSet dsPrint;
                //string AppCode = "OUTPDOCT";
                //hasParam.Add("ORDER_CLASS", listOrder[0].ORDER_CLASS);
                //hasParam.Add("CLINIC_NO", listOrder[0].CLINIC_NO);
                //hasParam.Add("APPOINT_NO", listOrder[0].APPOINT_NO);
                //hasParam.Add("OUTP_SERIAL_NO", listOrder[0].SERIAL_NO);
                ////20240506 增加打印参数PRESCNO及HIS_UNIT_CODE
                //hasParam.Add("HIS_UNIT_CODE", SystemParm.HisUnitCode);
                //string prescPage = SystemParm.GetParameterValue("PRESCNO", AppCode, "*", SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                //if (prescPage.Equals("0") || string.IsNullOrEmpty(prescPage))
                //{

                //}
                //else
                //{
                //    int k = int.Parse(prescPage);
                //    for (int i = 0; i < k; i++)
                //    {
                //        if (!string.IsNullOrEmpty(listOrder[0].SERIAL_NO))
                //        {
                //            dsPrint = XtraReportHelper.GetPrintData_DataBase("门诊医生处方单_普通中药", hasParam, AppCode);

                //            XtraReportHelper.Print("门诊医生处方单_普通中药", dsPrint, false, AppCode);
                //        }
                //    }

                //}
            }
            catch (Exception ex)
            {

                throw ex;
            }
            
        }

        // 打印注射单
        public void TransfusionClincked(object sender, MouseEventArgs e)
        {
            OUTP_ORDERS_STANDARD order = this.GetEditModel<OUTP_ORDERS_STANDARD>(this.bsPresc);
            if (order == null)
            {
                XtraMessageBox.Show("请选择要打印的处方", "提示");
                return;
            }
            else
            {
                if (order.APPOINT_NO == null || order.APPOINT_NO == string.Empty || order.APPOINT_NO.ToString() == "新开")
                {
                    XtraMessageBox.Show("请选择要打印的处方!", "提示");
                    return;
                }

                string outpserialno = order.OUTP_SERIAL_NO;
                string outpapp_no = order.APPOINT_NO;
                string order_dept = order.ORDERED_BY;
                if (!order_dept.Equals(GlobalValue.DeptCode))
                {
                    XtraMessageBox.Show("不是本科室开的处方不能打印", "提示");
                    return;
                }

                FrmPrintOutp fp = new FrmPrintOutp();
                fp.clinicNo = order.CLINIC_NO;
                fp.serialNo = outpserialno;
                fp.appointNo = outpapp_no;
                fp.AppCode = GlobalValue.AppCode;
                fp.prnType = "注射通知单";
                fp.ShowDialog();
            }
        }

        private void layoutView1_KeyDown(object sender, KeyEventArgs e)
        {
            GridColumn gridColumn = this.layoutView1.FocusedColumn;
            if (null == gridColumn)
            {
                return;
            }
            if (e.KeyCode == Keys.Enter && gridColumn.FieldName.Equals("DOSAGE"))
            {
                this.layoutView1.MoveNext();
                SendKeys.SendWait("{Tab}");
                SendKeys.SendWait("{Tab}");
                this.layoutView1.ShowEditor();
            }
        }

        private void keyBoard1_FilterValueKeyDown(KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                this.gvDrug.Focus();
            }
        }

        private void gvDrug_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (this.gvDrug.FocusedRowHandle >= 0)
                {
                    gvDrug_DoubleClick(null, null);
                    keyBoard1_FilterResult("");
                    keyBoard1.teInput.Focus();
                }
            }
        }

        private void layoutView1_VisibleRecordIndexChanged(object sender, DevExpress.XtraGrid.Views.Layout.Events.LayoutViewVisibleRecordIndexChangedEventArgs e)
        {
            //int dataNum = layoutView1.DataRowCount;
            ////计算显示的列数 
            //int showcol = layoutView1.OptionsMultiRecordMode.MaxCardColumns;// (layoutView1.ViewRect.Width / layoutView1.TemplateCard.MinSize.Width);
            ////计算显示的行数 
            //int showrow = (layoutView1.ViewRect.Height / layoutView1.TemplateCard.MinSize.Height)+1;
            ////计算显示的卡片数 
            //int cards = showcol * showrow;
            ////如果是向下滚动 
            //if ((e.VisibleRecordIndex > e.PrevVisibleRecordIndex))
            //{
            //    //如果是第一次滚动 
            //    if ((e.VisibleRecordIndex - e.PrevVisibleRecordIndex) == cards)
            //    {
            //        layoutView1.VisibleRecordIndex = e.VisibleRecordIndex +(showcol - 1);
            //    }
            //    //如果滚动差距小于列数 
            //    if ((e.VisibleRecordIndex - e.PrevVisibleRecordIndex) < (showcol - 1))
            //    {
            //        layoutView1.VisibleRecordIndex = e.PrevVisibleRecordIndex + showcol;
            //    }
            //}
            ////如果是向上滚动 
            //if ((e.VisibleRecordIndex < e.PrevVisibleRecordIndex))
            //{
            //    //如果是第一次滚动 
            //    if ((e.PrevVisibleRecordIndex - e.VisibleRecordIndex)+1 == cards)
            //    {
            //        layoutView1.VisibleRecordIndex = cards;
            //    } //如果滚动差距小于列数 
            //    if ((e.PrevVisibleRecordIndex - e.VisibleRecordIndex) < (cards - 1))
            //    {
            //        layoutView1.VisibleRecordIndex = e.PrevVisibleRecordIndex - showcol;
            //    }
            //}
        }

        private void layoutView1_ShowingEditor(object sender, System.ComponentModel.CancelEventArgs e)
        {
            this.ExcuteExcetionMethodChangeCursor(() =>
            {
                OUTP_ORDERS_STANDARD order = this.GetEditModel<OUTP_ORDERS_STANDARD>(this.bsPresc);
                List<OUTP_ORDERS_STANDARD> addOrders = this.bsPresc.Cast<OUTP_ORDERS_STANDARD>().ToList();
                //addOrders.Remove(order);
                string filedName = this.layoutView1.FocusedColumn.FieldName;
                e.Cancel = !OrderBusiness.CanShowEditor(order, addOrders, filedName);
            });
        }

        private void TxtPrescAttr_EditValueChanged(object sender, EventArgs e)
        {
            string prescAttr = "";
            if (TxtPrescAttr.EditValue != null && !string.IsNullOrEmpty(TxtPrescAttr.EditValue.ToString()))
                prescAttr = TxtPrescAttr.EditValue.ToString();
            if (prescAttr == "慢特处方")
            {
                luePespDise.Enabled = true;
                luePespDise.ReadOnly = false;
            }
            else
            {
                luePespDise.Enabled = false;
                luePespDise.EditValue = null;
            }
        }
        

        private bool CalculateYJDX(ref string strMsg,OUTP_ORDERS_STANDARD orderPara = null)
        {
            OUTP_ORDERS_STANDARD order = new OUTP_ORDERS_STANDARD();
            if (orderPara == null)
                order = this.GetEditModel<OUTP_ORDERS_STANDARD>(this.bsPresc);
            else
                order = orderPara;
            List<OUTP_ORDERS_STANDARD> addOrders = this.bsPresc.Cast<OUTP_ORDERS_STANDARD>().ToList();
            if (order == null)
            {
                return false;
            }
            //如果没有医嘱类别，则是代表改记录是新增的空记录
            if (string.IsNullOrEmpty(order.ORDER_CLASS))
            {
                return false;
            }

            // 添加CalculateYJDX方法入口日志
            try
            {
                string logPath = @"..\Client\LOG\exLOG\门诊医生站_进定销检查_" + DateTime.Now.ToString("yyyyMMdd") + ".log";
                string logEntry = string.Format(
                    "[{0:yyyy-MM-dd HH:mm:ss}] [INFO] [进定销检查] 开始检查 - 药品={1}, 当前剂量={2}g, 数量={3}\r\n",
                    DateTime.Now, order.ORDER_TEXT, order.DOSAGE, order.AMOUNT
                );
                System.IO.File.AppendAllText(logPath, logEntry);
            }
            catch { /* 忽略日志错误 */ }

            List<OUTP_ORDERS_STANDARD> removeOrderList = new List<OUTP_ORDERS_STANDARD>();
            List<OUTP_ORDERS_STANDARD> addOrderList = new List<OUTP_ORDERS_STANDARD>();

            if(order.ORDER_SUB_NO>1)
            {
                removeOrderList.Add(order);
            }
            else
            {
                if (!PrescBusiness.GetAmountChangeOrders(order, this.bsPresc.List.Cast<OUTP_ORDERS_STANDARD>().ToList(), addOrders, ref addOrderList, ref removeOrderList, ref strMsg))
                    return false;
            }
            if ((addOrderList != null && addOrderList.Count >= 1) || (removeOrderList != null && removeOrderList.Count >= 1))
            {
                foreach (OUTP_ORDERS_STANDARD orderRemove in removeOrderList)
                {
                    this.bsPresc.Remove(orderRemove);
                    this.OrderBusiness.Remove(orderRemove);
                    addOrders.Remove(orderRemove);
                }
                foreach (OUTP_ORDERS_STANDARD order1 in addOrderList)
                {
                    OUTP_ORDERS_STANDARD order2 = this.OrderBusiness.Add(order1);
                    //this.bsPresc.Add(order2);
                    List< OUTP_ORDERS_STANDARD > ordersTemp = this.bsPresc.List.Cast<OUTP_ORDERS_STANDARD>().ToList();
                    int idex = bsPresc.IndexOf(ordersTemp.Find(r => r.ORDER_NO == order2.ORDER_NO + 1));
                    if (ordersTemp.Count == 0 && ordersTemp.Find(r => r.ORDER_NO == order2.ORDER_NO + 1) == null || idex < 0)
                    {
                        bsPresc.Add(order2);
                    }
                    else
                    {
                        this.bsPresc.Insert(bsPresc.IndexOf(ordersTemp.Find(r => r.ORDER_NO == order.ORDER_NO + 1)), order2);
                    }
                    //PrescBusiness.CellValueChanged("DOSAGE", order2, addOrders);
                }

            }
            return true;
            //else
            //{
            //    PrescBusiness.CellValueChanged("DOSAGE", order, addOrders);
            //}
        }
        private void layoutView1_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            string strMsg = "";
            this.ExcuteExcetionMethodChangeCursor(() =>
            {
                OUTP_ORDERS_STANDARD order = this.GetEditModel<OUTP_ORDERS_STANDARD>(this.bsPresc);
                List<OUTP_ORDERS_STANDARD> addOrders = this.bsPresc.Cast<OUTP_ORDERS_STANDARD>().ToList();
                if (order == null)
                {
                    return;
                }
                //如果没有医嘱类别，则是代表改记录是新增的空记录
                if (string.IsNullOrEmpty(order.ORDER_CLASS))
                {
                    return;
                }
                string fieldName = e.Column.FieldName;

                // 添加界面事件日志，追踪剂量变化
                try
                {
                    string logPath = @"..\Client\LOG\exLOG\门诊医生站_界面事件_" + DateTime.Now.ToString("yyyyMMdd") + ".log";
                    string logEntry = string.Format(
                        "[{0:yyyy-MM-dd HH:mm:ss}] [INFO] [界面事件] 字段={1}, 药品={2}, 修改前剂量={3}g, 新值={4}\r\n",
                        DateTime.Now, fieldName, order.ORDER_TEXT, order.DOSAGE, e.Value
                    );
                    System.IO.File.AppendAllText(logPath, logEntry);
                }
                catch { /* 忽略日志错误 */ }
                if (order.ORDER_CLASS.Equals(OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE))
                {
                    int dataNum = layoutView1.DataRowCount;
                    //计算显示的列数 
                    int showcol = layoutView1.OptionsMultiRecordMode.MaxCardColumns;// (layoutView1.ViewRect.Width / layoutView1.TemplateCard.MinSize.Width);
                                                                                    //计算显示的行数 
                    int showrow = (layoutView1.ViewRect.Height / layoutView1.TemplateCard.MinSize.Height) + 1;
                    //计算显示的卡片数 
                    int cards = showcol * showrow;
                    int visibleRecordIndex = layoutView1.VisibleRecordIndex;
                    int cc=layoutView1.FocusedRowHandle;
                    if (fieldName == "DOSAGE")
                    {
                        List< OUTP_ORDERS_STANDARD > orders =  this.bsPresc.List.Cast<OUTP_ORDERS_STANDARD>().ToList();
                        this.PrescBusiness.SetCDrugProperty(this.GetEditModel<OUTP_ORDERS_STANDARD>(this.bsPrescList), order);
                        if(order.ORDER_SUB_NO>1)
                        {
                            OUTP_ORDERS_STANDARD orderBase = orders.Where(r => r.ORDER_SUB_NO == 1).FirstOrDefault();
                            orderBase.DOSAGE = order.DOSAGE;
                            this.PrescBusiness.SetCDrugProperty(this.GetEditModel<OUTP_ORDERS_STANDARD>(this.bsPrescList), orderBase);
                        }
                        if (!CalculateYJDX(ref strMsg) && !string.IsNullOrEmpty(strMsg))
                            throw new MessageException(strMsg);
                        this.bsPresc.ResetBindings(false);
                        if((cc+1)% cards == 0)
                            layoutView1.VisibleRecordIndex = visibleRecordIndex+ cards;
                        else
                            layoutView1.VisibleRecordIndex = visibleRecordIndex;
                    }
                    else
                    {
                        Decimal? originalDosage = order.DOSAGE;
                        PrescBusiness.CellValueChanged(fieldName, order, addOrders);
                        if(originalDosage != order.DOSAGE)
                        {
                            if (!CalculateYJDX(ref strMsg) && !string.IsNullOrEmpty(strMsg))
                                throw new MessageException(strMsg);
                            this.bsPresc.ResetBindings(false);
                        }
                    }
                }

            });
        }

        /// <summary>
        /// 写入调试日志到文件
        /// </summary>
        /// <param name="message">日志消息</param>
        private void WriteDebugLog(string message)
        {
            try
            {
                string logDir = System.IO.Path.Combine(Application.StartupPath, "LOG", "exLOG");
                if (!System.IO.Directory.Exists(logDir))
                {
                    System.IO.Directory.CreateDirectory(logDir);
                }

                string logFile = System.IO.Path.Combine(logDir, $"OrderDebug_{DateTime.Now:yyyyMMdd}.log");
                string logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] {message}";

                System.IO.File.AppendAllText(logFile, logMessage + Environment.NewLine);
            }
            catch
            {
                // 忽略日志写入错误，避免影响主要业务逻辑
            }
        }
    }
}
