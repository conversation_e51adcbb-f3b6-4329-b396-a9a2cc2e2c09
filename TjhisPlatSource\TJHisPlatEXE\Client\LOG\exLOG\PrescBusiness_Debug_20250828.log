[2025-08-28 17:05:51.175] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8091, 剂数: 1
[2025-08-28 17:05:51.176] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 0
[2025-08-28 17:05:51.177] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-28 17:05:51.345] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8090, 剂数: 1
[2025-08-28 17:05:51.345] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-28 17:05:51.345] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8090
[2025-08-28 17:05:51.348] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-28 17:05:51.360] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 砂仁, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-28 17:05:51.409] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-28 17:05:51.791] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 6742, 剂数: 1
[2025-08-28 17:05:51.791] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-28 17:05:51.792] [CDrugPresc.RepetitionChanged] 处理药品: 枸杞子, 处方号: 6742
[2025-08-28 17:05:51.792] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 枸杞子, 药品代码: 63080241YP1, 进入时dosage: 10g
[2025-08-28 17:05:51.793] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 枸杞子, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-28 17:05:51.844] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-28 17:05:51.931] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8090, 剂数: 1
[2025-08-28 17:05:51.932] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-28 17:05:51.932] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8090
[2025-08-28 17:05:51.933] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-28 17:05:51.933] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 砂仁, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-28 17:05:51.987] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-28 17:05:54.061] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8090, 剂数: 1
[2025-08-28 17:05:54.061] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 0
[2025-08-28 17:05:54.061] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-28 17:05:54.143] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 6742, 剂数: 1
[2025-08-28 17:05:54.143] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-28 17:05:54.143] [CDrugPresc.RepetitionChanged] 处理药品: 枸杞子, 处方号: 6742
[2025-08-28 17:05:54.144] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 枸杞子, 药品代码: 63080241YP1, 进入时dosage: 10g
[2025-08-28 17:05:54.144] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 枸杞子, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-28 17:05:54.206] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-28 17:05:54.525] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 6742, 剂数: 1
[2025-08-28 17:05:54.525] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-28 17:05:54.526] [CDrugPresc.RepetitionChanged] 处理药品: 枸杞子, 处方号: 6742
[2025-08-28 17:05:54.526] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 枸杞子, 药品代码: 63080241YP1, 进入时dosage: 10g
[2025-08-28 17:05:54.526] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 枸杞子, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-28 17:05:54.594] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-28 17:05:54.674] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 6742, 剂数: 1
[2025-08-28 17:05:54.674] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-28 17:05:54.675] [CDrugPresc.RepetitionChanged] 处理药品: 枸杞子, 处方号: 6742
[2025-08-28 17:05:54.676] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 枸杞子, 药品代码: 63080241YP1, 进入时dosage: 10g
[2025-08-28 17:05:54.676] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 枸杞子, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-28 17:05:54.676] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-28 17:06:02.818] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 1.0g
[2025-08-28 17:06:02.820] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-28 17:06:02.820] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 1.0g, dosagePerUnit: 1.0, AMOUNT_PER_PACKAGE: 1
[2025-08-28 17:06:02.820] [PrescBusiness.CalculateSplitDosage] 检查点[可能的错误重置] - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 1.0g, 可能已被错误重置
[2025-08-28 17:06:02.820] [PrescBusiness.CalculateSplitDosage] 重新计算dosage - 原值: 1.0g, 新值: 1.0g, dosagePerUnit: 1.0
[2025-08-28 17:06:02.821] [PrescBusiness.CalculateSplitDosage] 方法结束 - 最终返回dosage: 1.0g
[2025-08-28 17:06:02.821] [CDrugPresc.SetAmout] CalculateSplitDosage返回 - 药品名称: 砂仁, 返回dosage: 1.0g, 原始dosage: 1.0g
[2025-08-28 17:06:10.295] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-28 17:06:10.296] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-28 17:06:10.296] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 10g, dosagePerUnit: 1.0, AMOUNT_PER_PACKAGE: 1
[2025-08-28 17:06:10.297] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 10g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-28 17:06:10.297] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 10g
[2025-08-28 17:06:10.350] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-28 17:06:10.350] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-28 17:06:10.350] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 10g, dosagePerUnit: 1.0, AMOUNT_PER_PACKAGE: 1
[2025-08-28 17:06:10.350] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 10g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-28 17:06:10.351] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 10g
[2025-08-28 17:06:11.686] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-28 17:06:11.686] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-28 17:06:11.686] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 10g, dosagePerUnit: 1.0, AMOUNT_PER_PACKAGE: 1
[2025-08-28 17:06:11.687] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 10g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-28 17:06:11.687] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 10g
[2025-08-28 17:06:11.900] [CDrugPresc.AutoDividePresc] 分方前状态 - 医嘱数量: 1
[2025-08-28 17:06:11.901] [CDrugPresc.AutoDividePresc] 分方前状态[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 3, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 17:06:11.922] [CDrugPresc.AutoDividePresc] 检查点[开始分方] - AutoDetachDrug参数: 1
[2025-08-28 17:06:11.922] [CDrugPresc.AutoDividePresc] 开始执行库存检查
[2025-08-28 17:06:12.069] [CDrugPresc.AutoDividePresc] 库存检查通过
[2025-08-28 17:06:12.160] [CDrugPresc.AutoDividePresc] 检查点[处方分组] - 处方号: 8092, 分配ORDER_NO: 4, 中药数量: 1
[2025-08-28 17:06:12.161] [CDrugPresc.AutoDividePresc] 检查点[ORDER_SUB_NO分配] - ORDER_TEXT: 砂仁, ORDER_NO: 4, ORDER_SUB_NO: 1
[2025-08-28 17:06:12.161] [CDrugPresc.AutoDividePresc] 检查点[费用记录处理] - ORDER_TEXT: 砂仁, ORDER_NO: 4, ORDER_SUB_NO: 1, 费用记录数量: 1
[2025-08-28 17:06:12.162] [CDrugPresc.AutoDividePresc] 检查点[费用记录修改前] - ORDER_TEXT: 砂仁, 原ORDER_NO: 3, 原ORDER_SUB_NO: 1, 原ITEM_NO: 1
[2025-08-28 17:06:12.162] [CDrugPresc.AutoDividePresc] 检查点[费用ITEM_NO分配] - ORDER_TEXT: 砂仁, 新ORDER_NO: 4, 新ORDER_SUB_NO: 1, 新费用ITEM_NO: 1, CLINIC_NO: 250717000ADMIN00001
[2025-08-28 17:06:12.162] [CDrugPresc.AutoDividePresc] 分方后状态 - 医嘱数量: 1
[2025-08-28 17:06:12.162] [CDrugPresc.AutoDividePresc] 分方后状态[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 4, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 17:06:12.163] [CDrugPresc.AutoDividePresc] 检查点[分方完成] - 成功
[2025-08-28 17:06:12.875] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 新方, 剂数: 1
[2025-08-28 17:06:12.876] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 0
[2025-08-28 17:06:12.876] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-28 17:06:13.033] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8092, 剂数: 1
[2025-08-28 17:06:13.033] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-28 17:06:13.033] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8092
[2025-08-28 17:06:13.034] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-28 17:06:13.034] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-28 17:06:13.034] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 10g, dosagePerUnit: 1, AMOUNT_PER_PACKAGE: 0
[2025-08-28 17:06:13.034] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 10g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-28 17:06:13.035] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 10g
[2025-08-28 17:06:13.086] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-28 17:06:14.006] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 6742, 剂数: 1
[2025-08-28 17:06:14.006] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-28 17:06:14.007] [CDrugPresc.RepetitionChanged] 处理药品: 枸杞子, 处方号: 6742
[2025-08-28 17:06:14.007] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 枸杞子, 药品代码: 63080241YP1, 进入时dosage: 10g
[2025-08-28 17:06:14.008] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 枸杞子, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-28 17:06:14.129] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-28 17:06:14.214] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8092, 剂数: 1
[2025-08-28 17:06:14.214] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-28 17:06:14.215] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8092
[2025-08-28 17:06:14.215] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-28 17:06:14.215] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-28 17:06:14.216] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 10g, dosagePerUnit: 1, AMOUNT_PER_PACKAGE: 0
[2025-08-28 17:06:14.216] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 10g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-28 17:06:14.216] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 10g
[2025-08-28 17:06:14.265] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-28 17:06:15.025] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8092, 剂数: 1
[2025-08-28 17:06:15.026] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-28 17:06:15.026] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8092
[2025-08-28 17:06:15.027] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-28 17:06:15.027] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-28 17:06:15.027] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 10g, dosagePerUnit: 1, AMOUNT_PER_PACKAGE: 0
[2025-08-28 17:06:15.027] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 10g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-28 17:06:15.027] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 10g
[2025-08-28 17:06:15.067] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-28 17:06:15.153] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8092, 剂数: 1
[2025-08-28 17:06:15.153] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-28 17:06:15.154] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8092
[2025-08-28 17:06:15.154] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-28 17:06:15.155] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-28 17:06:15.155] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 10g, dosagePerUnit: 1, AMOUNT_PER_PACKAGE: 1
[2025-08-28 17:06:15.155] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 10g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-28 17:06:15.155] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 10g
[2025-08-28 17:06:15.156] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-28 17:06:21.017] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 1.0g
[2025-08-28 17:06:21.017] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-28 17:06:21.017] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 1.0g, dosagePerUnit: 1.0, AMOUNT_PER_PACKAGE: 1
[2025-08-28 17:06:21.017] [PrescBusiness.CalculateSplitDosage] 检查点[可能的错误重置] - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 1.0g, 可能已被错误重置
[2025-08-28 17:06:21.018] [PrescBusiness.CalculateSplitDosage] 重新计算dosage - 原值: 1.0g, 新值: 1.0g, dosagePerUnit: 1.0
[2025-08-28 17:06:21.018] [PrescBusiness.CalculateSplitDosage] 方法结束 - 最终返回dosage: 1.0g
[2025-08-28 17:06:21.018] [CDrugPresc.SetAmout] CalculateSplitDosage返回 - 药品名称: 砂仁, 返回dosage: 1.0g, 原始dosage: 1.0g
[2025-08-28 17:06:27.154] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 12g
[2025-08-28 17:06:27.154] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-28 17:06:27.154] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 12g, dosagePerUnit: 1.0, AMOUNT_PER_PACKAGE: 1
[2025-08-28 17:06:27.154] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 12g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-28 17:06:27.155] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 12g
[2025-08-28 17:06:27.788] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 12g
[2025-08-28 17:06:27.788] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-28 17:06:27.788] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 12g, dosagePerUnit: 1.0, AMOUNT_PER_PACKAGE: 1
[2025-08-28 17:06:27.788] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 12g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-28 17:06:27.789] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 12g
