2025-08-28 16:06:51
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:51
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:51
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:06:51
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:51
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:51
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:51
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:51
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:51
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:51
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:06:51
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:51
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:51
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:52
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:52
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:52
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:52
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:52
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:52
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:52
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:52
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:52
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:53
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:53
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:53
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:53
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:53
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:53
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:53
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:53
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:53
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:54
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:54
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:54
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:54
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:54
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:54
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:54
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:54
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:54
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:55
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:55
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:55
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:55
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:55
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:55
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:55
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:55
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:55
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:56
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:56
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:56
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:56
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:56
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:56
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:56
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:56
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:56
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:57
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:57
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:57
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:57
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:57
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:57
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:57
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:57
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:57
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:58
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:58
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:58
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:58
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:58
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:58
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:58
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:58
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:58
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:59
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:59
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:59
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:59
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:59
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:59
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:59
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:59
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:59
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:00
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:00
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:00
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:00
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:00
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:00
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:00
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:00
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:00
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:01
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:01
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:01
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:01
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:01
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:01
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:01
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:01
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:01
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:02
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:02
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:02
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:02
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:02
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:02
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:02
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:02
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:02
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:03
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:03
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:03
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:03
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:03
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:03
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:03
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:03
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:03
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:04
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:04
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:04
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:04
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:04
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:04
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:04
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:04
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:04
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:05
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:05
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:05
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:05
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:05
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:05
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:05
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:05
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:05
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:06
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:06
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:06
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:06
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:06
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:06
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:06
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:06
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:06
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:36:41
SignData，hisUnitCode:45038900950011711A6001,appName:OUTPDOCT,deptCode:020101,signType:OutpCnDrugPresc,userId:ADMIN,userId:门诊中药处方
-----------------------------------
2025-08-28 16:36:41
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:36:41
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:36:41
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:36:41
getOauthByUserId，url:http://***********:8091/doctor/api/v1.0/auth/getOauthByUserId?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"userId":"ADMIN"}
-----------------------------------
2025-08-28 16:36:41
getOauthByUserId，code:0;reslut:{"status":"0","message":"success","data":[{"oauthStatus":"1","userId":"ADMIN","userNumExpands":null,"userName":"张华","certDN":"C=CN,O=重庆市革命伤残军人康复医院,OU=e0165affc55614fe9b4389c6fdf200cd||ADMIN,CN=张华","certSN":"1002020325082116d28bc990349cf0169857","certStartTime":"2025-08-21 16:40:06","certEndTime":"2026-08-21 16:40:06","oauthSignature":null,"authKEY":"********************************","authTime":300,"expireTime":15882,"authStartTime":"2025-08-28 16:01:23","authEndTime":"2025-08-28 21:01:23","idcard":"511024199511180756","transactionId":"yxnwm4d2oqcr5d5w","userPhone":"18010547730","officeName":"测试病区","callbackURL":"","authType":"1","oauthMethod":"10","optUserId":"ADMIN","signatureImg":"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","imageUpdateTime":"2024-08-20 16:59:35","sign":null,"officeQyId":""}]}
-----------------------------------
2025-08-28 16:36:41
SignData，getSignData:OutpCnDrugPresc,data: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
-----------------------------------
2025-08-28 16:36:41
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:36:41
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:36:41
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:36:41
signData，url:http://***********:8091/doctor/api/v1.0/sign/signdata?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"userId":"ADMIN","transactionId":"OutpCnDrugPresc|0000000071|11014386|250717000ADMIN00001|4|1","authKEY":"********************************","fileName":"门诊中药处方","data":"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","isHash":"0","timestamp":"1","isBackTimestamp":"1","isBackSignatureImg":"1","isBackSignCert":"1"}
-----------------------------------
2025-08-28 16:36:41
signData，code:0;reslut:{"status":"0","message":"success","data":{"uniqueIdent":null,"fileCode":"202508-7926a0f7749d45469787557874a376fc5","fileName":"门诊中药处方","signedData":"MEUCIQCsMrPDkfsunkkzKBhAvKE8j5UvUvc/VyMIHK8ijMtq7QIgcH1G2iIYcbC60h6mptTuZ0aehXd0y4dGtODogmXmqLM=","signTime":"2025-08-28 16:36:40","certSN":"1002020325082116d28bc990349cf0169857","certDN":"C=CN,O=重庆市革命伤残军人康复医院,OU=e0165affc55614fe9b4389c6fdf200cd||ADMIN,CN=张华","certStartTime":"2025-08-21 16:40:06","certEndTime":"2026-08-21 16:40:06","certIssuer":"Taier SM2 CA","signatureImg":"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","imageUpdateTime":"2024-08-20 16:59:35","signCert":"MIIEizCCBDCgAwIBAgISEAICAyUIIRbSi8mQNJzwFphXMAwGCCqBHM9VAYN1BQAwRzELMAkGA1UEBhMCQ04xITAfBgNVBAoMGFRMQyBDZXJ0aWZpY2F0aW9uIENlbnRlcjEVMBMGA1UEAwwMVGFpZXIgU00yIENBMB4XDTI1MDgyMTA4NDAwNloXDTI2MDgyMTA4NDAwNlowgYIxCzAJBgNVBAYTAkNOMTAwLgYDVQQKDCfph43luobluILpnanlkb3kvKTmrovlhpvkurrlurflpI3ljLvpmaIxMDAuBgNVBAsMJ2UwMTY1YWZmYzU1NjE0ZmU5YjQzODljNmZkZjIwMGNkfHxBRE1JTjEPMA0GA1UEAwwG5byg5Y2OMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEvXfghws+gMv/BXSUbeY3VkOaNQc62ajem6keCkMYVuHQuW1d0naitVEKIh4PHv+CN2JGvQvAQvrS3+wIs5dbWKOCArwwggK4MA4GA1UdDwEB/wQEAwIGwDAJBgNVHRMEAjAAMA4GBiqBHNAeAwQEWFhYWDCBowYDVR0fBIGbMIGYME+gTaBLpEkwRzEVMBMGA1UEAwwMVGFpZXIgU00yIENBMSEwHwYDVQQKDBhUTEMgQ2VydGlmaWNhdGlvbiBDZW50ZXIxCzAJBgNVBAYTAkNOMEWgQ6BBhj9odHRwOi8vbGRhcC5jYS50bGMuY29tLmNuL2Nhd2ViL2NybC9UYWllclNNMkNBL1RhaWVyU00yQ0FfMC5jcmwwHQYDVR0OBBYEFDFLjdWedmegtqqTNzaeW+FWhzYRMB8GA1UdIwQYMBaAFCw9JkPK6QG/rx2xUXNm0U5E2LOhMH8GA1UdIAR4MHYwdAYKKwYBBAGD2EkBATBmMGQGCCsGAQUFBwIBFlhodHRwOi8vd3d3LnRsYy5jb20uY24vRmlsZVN5c1B1Yi9VcGxvYWQvTmV3cy9GaWxlLzIwMjEwOTE1LzYzNzY3MzExMDc2MjcwMjk5MDMxOTQ4MTYucGRmMIGGBggrBgEFBQcBAQR6MHgwOwYIKwYBBQUHMAKGL2h0dHA6Ly9jZXJ0LmNhLnRsYy5jb20uY24vY2FmaWxlL3RhaWVyc20yY2EuY3J0MDkGCCsGAQUFBzABhi1odHRwOi8vb2NzcC5jYS50bGMuY29tLmNuOjE3MDYwL29jc3B3ZWIvb2NzcC8wMQYKKoEchu8yAgEBFwQjDBQxQDEwNTVTRjFOVEV4TURJME1UazVOVEV4TVRnd056VTIwIgYIYIZIAYb4RAIEFgwUU0Y1MTEwMjQxOTk1MTExODA3NTYwMQYKKoEchu8yAgEBAQQjDBQxQDEwNTVTRjFOVEV4TURJME1UazVOVEV4TVRnd056VTIwEQYGKlYLBwEIBAcMFEFETUlOMAwGCCqBHM9VAYN1BQADRwAwRAIgTsohIESpBwnTT6zkmYfgycvkLJR/JIBUk/iesgeMnLACIAz67sgnGcp0owYQkNNK9KGeKQ7szg5XY6yhp/Hk9jzg","hash":null,"timestamp":"MIIFlDADAgEAMIIFiwYKKoEcz1UGAQQCAqCCBXswggV3AgEDMQ4wDAYIKoEcz1UBgxEFADCBxgYLKoZIhvcNAQkQAQSggbYEgbMwgbACAQEGASowMDAMBggqgRzPVQGDEQUABCDj0Ez5oRzOwQMhMfrEzOlvPwFXSAikP/s5CTLlTsXASgIIeAiUEHS6AAAYDzIwMjUwODI4MDgzNjQwWgEB/wIIeAiUEHR6AACgTqRMMEoxCzAJBgNVBAYTAkNOMQ8wDQYDVQQKDAZTaWduZXIxFzAVBgNVBAsMDlRpbWV8fFN0YW1waW5nMREwDwYDVQQDDAhNZWRpc2lnbqCCAr8wggK7MIICYqADAgECAhYigTU1A0USAVNhMBBXaDkpgHNickQXMAoGCCqBHM9VAYN1MIGYMRYwFAYDVQQDDA1ITEpDQV9TTTJfU1VCMQ4wDAYDVQQLDAVITEpDQTEzMDEGA1UECgwq6buR6b6Z5rGf55yB5pWw5a2X6K+B5Lmm6K6k6K+B5pyJ6ZmQ5YWs5Y+4MRUwEwYDVQQHDAzlk4jlsJTmu6jluIIxFTATBgNVBAgMDOm7kem+meaxn+ecgTELMAkGA1UEBhMCQ04wHhcNMjQwNDIxMTE0MDUyWhcNMjcwNDAxMTE0MDUyWjBKMQswCQYDVQQGEwJDTjEPMA0GA1UECgwGU2lnbmVyMRcwFQYDVQQLDA5UaW1lfHxTdGFtcGluZzERMA8GA1UEAwwITWVkaXNpZ24wWTATBgcqhkjOPQIBBggqgRzPVQGCLQNCAAQKOqfpOO9IN5w+d/syK0p1x9nN5kA+uY8eCx90pxv3pvieUR9pD/LmLbhlL5w/KGZUM3nU8+pd0e1YdJpQpQtRo4HUMIHRMB0GA1UdDgQWBBRdyNm4aGshfm6TZhyvRPWtX/JDhzALBgNVHQ8EBAMCBsAwHwYDVR0jBBgwFoAUMKhsmrIyB0dDRdSLhcQ7DZU+ff8wFgYDVR0lAQH/BAwwCgYIKwYBBQUHAwgwIgYIYIZIAYb4RAIEFgwUU0Y5MTQ0MDMwMDM1OTk2MTA4NkEwJAYKKoEchu8yAgEBAQQWDBRTRjkxNDQwMzAwMzU5OTYxMDg2QTAgBgYqVgsHAQgEFgwUU0Y5MTQ0MDMwMDM1OTk2MTA4NkEwCgYIKoEcz1UBg3UDRwAwRAIgLqBZZwWG2p3R2insocMeewoJVdEkT4LwDDCqb7ujfhYCIASehhySMz53jrvmJhRVyO1sNpRgsR0v/DVIpe2KNC6MMYIB1DCCAdACAQEwgbMwgZgxFjAUBgNVBAMMDUhMSkNBX1NNMl9TVUIxDjAMBgNVBAsMBUhMSkNBMTMwMQYDVQQKDCrpu5HpvpnmsZ/nnIHmlbDlrZfor4HkuaborqTor4HmnInpmZDlhazlj7gxFTATBgNVBAcMDOWTiOWwlOa7qOW4gjEVMBMGA1UECAwM6buR6b6Z5rGf55yBMQswCQYDVQQGEwJDTgIWIoE1NQNFEgFTYTAQV2g5KYBzYnJEFzAMBggqgRzPVQGDEQUAoIGwMBoGCSqGSIb3DQEJAzENBgsqhkiG9w0BCRABBDAcBgkqhkiG9w0BCQUxDxcNMjUwODI4MDgzNjQwWjAvBgkqhkiG9w0BCQQxIgQgppSpkE9CCE5rJr+6W46hbxHmqrLy24y6oQfG/H4pIuEwQwYLKoZIhvcNAQkQAi8xNDAyMDAwLjAKBggqgRzPVQGDEQQgQ+r5Yf/8GZYPadH7UCbo+2A7fAT4gT1S3zF/7+EDLcMwDAYIKoEcz1UBg3UFAARGMEQCIDvrsFtI2QSjjzHnP5/C9rg+Kj/QyeJdVj7U1WnHR39sAiB8C6kLSvnzKKqivKoB8Hje1iX5PzKavCjg3t5IntDSEA=="}}
-----------------------------------
2025-08-28 16:36:58
SignData，hisUnitCode:45038900950011711A6001,appName:OUTPDOCT,deptCode:020101,signType:OutpCnDrugPresc,userId:ADMIN,userId:门诊中药处方
-----------------------------------
2025-08-28 16:36:59
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:36:59
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:36:59
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:36:59
getOauthByUserId，url:http://***********:8091/doctor/api/v1.0/auth/getOauthByUserId?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"userId":"ADMIN"}
-----------------------------------
2025-08-28 16:36:59
getOauthByUserId，code:0;reslut:{"status":"0","message":"success","data":[{"oauthStatus":"1","userId":"ADMIN","userNumExpands":null,"userName":"张华","certDN":"C=CN,O=重庆市革命伤残军人康复医院,OU=e0165affc55614fe9b4389c6fdf200cd||ADMIN,CN=张华","certSN":"1002020325082116d28bc990349cf0169857","certStartTime":"2025-08-21 16:40:06","certEndTime":"2026-08-21 16:40:06","oauthSignature":null,"authKEY":"********************************","authTime":300,"expireTime":15865,"authStartTime":"2025-08-28 16:01:23","authEndTime":"2025-08-28 21:01:23","idcard":"511024199511180756","transactionId":"yxnwm4d2oqcr5d5w","userPhone":"18010547730","officeName":"测试病区","callbackURL":"","authType":"1","oauthMethod":"10","optUserId":"ADMIN","signatureImg":"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","imageUpdateTime":"2024-08-20 16:59:35","sign":null,"officeQyId":""}]}
-----------------------------------
2025-08-28 16:36:59
SignData，getSignData:OutpCnDrugPresc,data: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
-----------------------------------
2025-08-28 16:36:59
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:36:59
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:36:59
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:36:59
signData，url:http://***********:8091/doctor/api/v1.0/sign/signdata?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"userId":"ADMIN","transactionId":"OutpCnDrugPresc|0000000071|11014386|250717000ADMIN00001|6|1","authKEY":"********************************","fileName":"门诊中药处方","data":"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","isHash":"0","timestamp":"1","isBackTimestamp":"1","isBackSignatureImg":"1","isBackSignCert":"1"}
-----------------------------------
2025-08-28 16:36:59
signData，code:0;reslut:{"status":"0","message":"success","data":{"uniqueIdent":null,"fileCode":"202508-92bb9f9bf5a24714a042cfbed85dce9c5","fileName":"门诊中药处方","signedData":"MEYCIQDMzpHAJrXtDiiH5GIYtqC1zdhI5KFx6FEQoIepsNpMcAIhAJppbZIzl6GKUrZABaTo9Z8ZGrBpzssBFpry9InxlPy2","signTime":"2025-08-28 16:36:57","certSN":"1002020325082116d28bc990349cf0169857","certDN":"C=CN,O=重庆市革命伤残军人康复医院,OU=e0165affc55614fe9b4389c6fdf200cd||ADMIN,CN=张华","certStartTime":"2025-08-21 16:40:06","certEndTime":"2026-08-21 16:40:06","certIssuer":"Taier SM2 CA","signatureImg":"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","imageUpdateTime":"2024-08-20 16:59:35","signCert":"MIIEizCCBDCgAwIBAgISEAICAyUIIRbSi8mQNJzwFphXMAwGCCqBHM9VAYN1BQAwRzELMAkGA1UEBhMCQ04xITAfBgNVBAoMGFRMQyBDZXJ0aWZpY2F0aW9uIENlbnRlcjEVMBMGA1UEAwwMVGFpZXIgU00yIENBMB4XDTI1MDgyMTA4NDAwNloXDTI2MDgyMTA4NDAwNlowgYIxCzAJBgNVBAYTAkNOMTAwLgYDVQQKDCfph43luobluILpnanlkb3kvKTmrovlhpvkurrlurflpI3ljLvpmaIxMDAuBgNVBAsMJ2UwMTY1YWZmYzU1NjE0ZmU5YjQzODljNmZkZjIwMGNkfHxBRE1JTjEPMA0GA1UEAwwG5byg5Y2OMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEvXfghws+gMv/BXSUbeY3VkOaNQc62ajem6keCkMYVuHQuW1d0naitVEKIh4PHv+CN2JGvQvAQvrS3+wIs5dbWKOCArwwggK4MA4GA1UdDwEB/wQEAwIGwDAJBgNVHRMEAjAAMA4GBiqBHNAeAwQEWFhYWDCBowYDVR0fBIGbMIGYME+gTaBLpEkwRzEVMBMGA1UEAwwMVGFpZXIgU00yIENBMSEwHwYDVQQKDBhUTEMgQ2VydGlmaWNhdGlvbiBDZW50ZXIxCzAJBgNVBAYTAkNOMEWgQ6BBhj9odHRwOi8vbGRhcC5jYS50bGMuY29tLmNuL2Nhd2ViL2NybC9UYWllclNNMkNBL1RhaWVyU00yQ0FfMC5jcmwwHQYDVR0OBBYEFDFLjdWedmegtqqTNzaeW+FWhzYRMB8GA1UdIwQYMBaAFCw9JkPK6QG/rx2xUXNm0U5E2LOhMH8GA1UdIAR4MHYwdAYKKwYBBAGD2EkBATBmMGQGCCsGAQUFBwIBFlhodHRwOi8vd3d3LnRsYy5jb20uY24vRmlsZVN5c1B1Yi9VcGxvYWQvTmV3cy9GaWxlLzIwMjEwOTE1LzYzNzY3MzExMDc2MjcwMjk5MDMxOTQ4MTYucGRmMIGGBggrBgEFBQcBAQR6MHgwOwYIKwYBBQUHMAKGL2h0dHA6Ly9jZXJ0LmNhLnRsYy5jb20uY24vY2FmaWxlL3RhaWVyc20yY2EuY3J0MDkGCCsGAQUFBzABhi1odHRwOi8vb2NzcC5jYS50bGMuY29tLmNuOjE3MDYwL29jc3B3ZWIvb2NzcC8wMQYKKoEchu8yAgEBFwQjDBQxQDEwNTVTRjFOVEV4TURJME1UazVOVEV4TVRnd056VTIwIgYIYIZIAYb4RAIEFgwUU0Y1MTEwMjQxOTk1MTExODA3NTYwMQYKKoEchu8yAgEBAQQjDBQxQDEwNTVTRjFOVEV4TURJME1UazVOVEV4TVRnd056VTIwEQYGKlYLBwEIBAcMFEFETUlOMAwGCCqBHM9VAYN1BQADRwAwRAIgTsohIESpBwnTT6zkmYfgycvkLJR/JIBUk/iesgeMnLACIAz67sgnGcp0owYQkNNK9KGeKQ7szg5XY6yhp/Hk9jzg","hash":null,"timestamp":"MIIFlDADAgEAMIIFiwYKKoEcz1UGAQQCAqCCBXswggV3AgEDMQ4wDAYIKoEcz1UBgxEFADCBxgYLKoZIhvcNAQkQAQSggbYEgbMwgbACAQEGASowMDAMBggqgRzPVQGDEQUABCCQD6UJX1JfLNAMolTp5Z28BbVUnPRiIBtcYH4bBFfi5wIIeAiUIZu6AAAYDzIwMjUwODI4MDgzNjU3WgEB/wIIeAiUIZt6AACgTqRMMEoxCzAJBgNVBAYTAkNOMQ8wDQYDVQQKDAZTaWduZXIxFzAVBgNVBAsMDlRpbWV8fFN0YW1waW5nMREwDwYDVQQDDAhNZWRpc2lnbqCCAr8wggK7MIICYqADAgECAhYigTU1A0USAVNhMBBXaDkpgHNickQXMAoGCCqBHM9VAYN1MIGYMRYwFAYDVQQDDA1ITEpDQV9TTTJfU1VCMQ4wDAYDVQQLDAVITEpDQTEzMDEGA1UECgwq6buR6b6Z5rGf55yB5pWw5a2X6K+B5Lmm6K6k6K+B5pyJ6ZmQ5YWs5Y+4MRUwEwYDVQQHDAzlk4jlsJTmu6jluIIxFTATBgNVBAgMDOm7kem+meaxn+ecgTELMAkGA1UEBhMCQ04wHhcNMjQwNDIxMTE0MDUyWhcNMjcwNDAxMTE0MDUyWjBKMQswCQYDVQQGEwJDTjEPMA0GA1UECgwGU2lnbmVyMRcwFQYDVQQLDA5UaW1lfHxTdGFtcGluZzERMA8GA1UEAwwITWVkaXNpZ24wWTATBgcqhkjOPQIBBggqgRzPVQGCLQNCAAQKOqfpOO9IN5w+d/syK0p1x9nN5kA+uY8eCx90pxv3pvieUR9pD/LmLbhlL5w/KGZUM3nU8+pd0e1YdJpQpQtRo4HUMIHRMB0GA1UdDgQWBBRdyNm4aGshfm6TZhyvRPWtX/JDhzALBgNVHQ8EBAMCBsAwHwYDVR0jBBgwFoAUMKhsmrIyB0dDRdSLhcQ7DZU+ff8wFgYDVR0lAQH/BAwwCgYIKwYBBQUHAwgwIgYIYIZIAYb4RAIEFgwUU0Y5MTQ0MDMwMDM1OTk2MTA4NkEwJAYKKoEchu8yAgEBAQQWDBRTRjkxNDQwMzAwMzU5OTYxMDg2QTAgBgYqVgsHAQgEFgwUU0Y5MTQ0MDMwMDM1OTk2MTA4NkEwCgYIKoEcz1UBg3UDRwAwRAIgLqBZZwWG2p3R2insocMeewoJVdEkT4LwDDCqb7ujfhYCIASehhySMz53jrvmJhRVyO1sNpRgsR0v/DVIpe2KNC6MMYIB1DCCAdACAQEwgbMwgZgxFjAUBgNVBAMMDUhMSkNBX1NNMl9TVUIxDjAMBgNVBAsMBUhMSkNBMTMwMQYDVQQKDCrpu5HpvpnmsZ/nnIHmlbDlrZfor4HkuaborqTor4HmnInpmZDlhazlj7gxFTATBgNVBAcMDOWTiOWwlOa7qOW4gjEVMBMGA1UECAwM6buR6b6Z5rGf55yBMQswCQYDVQQGEwJDTgIWIoE1NQNFEgFTYTAQV2g5KYBzYnJEFzAMBggqgRzPVQGDEQUAoIGwMBoGCSqGSIb3DQEJAzENBgsqhkiG9w0BCRABBDAcBgkqhkiG9w0BCQUxDxcNMjUwODI4MDgzNjU3WjAvBgkqhkiG9w0BCQQxIgQgQZWvwx+tRcH9QwU59zOsFc/1O4BIxTBUDAHz6zRYVKswQwYLKoZIhvcNAQkQAi8xNDAyMDAwLjAKBggqgRzPVQGDEQQgQ+r5Yf/8GZYPadH7UCbo+2A7fAT4gT1S3zF/7+EDLcMwDAYIKoEcz1UBg3UFAARGMEQCIBabo+2c0ikNSP9YjbTTps74R006Cn6zncTNdlnq2SbxAiBvS4QmXxPmYSPbRWdQXZR9fXps36oHRjn7xhWqD+B1Dw=="}}
-----------------------------------
2025-08-28 16:45:25
SignData，hisUnitCode:45038900950011711A6001,appName:OUTPDOCT,deptCode:020101,signType:OutpCnDrugPresc,userId:ADMIN,userId:门诊中药处方
-----------------------------------
2025-08-28 16:45:25
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:45:25
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:45:25
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:45:25
getOauthByUserId，url:http://***********:8091/doctor/api/v1.0/auth/getOauthByUserId?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"userId":"ADMIN"}
-----------------------------------
2025-08-28 16:45:25
getOauthByUserId，code:0;reslut:{"status":"0","message":"success","data":[{"oauthStatus":"1","userId":"ADMIN","userNumExpands":null,"userName":"张华","certDN":"C=CN,O=重庆市革命伤残军人康复医院,OU=e0165affc55614fe9b4389c6fdf200cd||ADMIN,CN=张华","certSN":"1002020325082116d28bc990349cf0169857","certStartTime":"2025-08-21 16:40:06","certEndTime":"2026-08-21 16:40:06","oauthSignature":null,"authKEY":"********************************","authTime":300,"expireTime":15359,"authStartTime":"2025-08-28 16:01:23","authEndTime":"2025-08-28 21:01:23","idcard":"511024199511180756","transactionId":"yxnwm4d2oqcr5d5w","userPhone":"18010547730","officeName":"测试病区","callbackURL":"","authType":"1","oauthMethod":"10","optUserId":"ADMIN","signatureImg":"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","imageUpdateTime":"2024-08-20 16:59:35","sign":null,"officeQyId":""}]}
-----------------------------------
2025-08-28 16:45:25
SignData，getSignData:OutpCnDrugPresc,data: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
-----------------------------------
2025-08-28 16:45:25
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:45:25
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:45:25
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:45:25
signData，url:http://***********:8091/doctor/api/v1.0/sign/signdata?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"userId":"ADMIN","transactionId":"OutpCnDrugPresc|0000000071|11014386|250717000ADMIN00001|4|1","authKEY":"********************************","fileName":"门诊中药处方","data":"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","isHash":"0","timestamp":"1","isBackTimestamp":"1","isBackSignatureImg":"1","isBackSignCert":"1"}
-----------------------------------
2025-08-28 16:45:25
signData，code:0;reslut:{"status":"0","message":"success","data":{"uniqueIdent":null,"fileCode":"202508-c547b95e7722466f8f52ad1e7dcb69575","fileName":"门诊中药处方","signedData":"MEYCIQC3Uc5eo4LX+DUq/TC8GsIh4hLoBr46DBpwTKiWWrK30AIhANv6lMtI5HTRPLQRUq7Rq2K7oKW/8a3JcwACF9jRYE4p","signTime":"2025-08-28 16:45:24","certSN":"1002020325082116d28bc990349cf0169857","certDN":"C=CN,O=重庆市革命伤残军人康复医院,OU=e0165affc55614fe9b4389c6fdf200cd||ADMIN,CN=张华","certStartTime":"2025-08-21 16:40:06","certEndTime":"2026-08-21 16:40:06","certIssuer":"Taier SM2 CA","signatureImg":"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","imageUpdateTime":"2024-08-20 16:59:35","signCert":"MIIEizCCBDCgAwIBAgISEAICAyUIIRbSi8mQNJzwFphXMAwGCCqBHM9VAYN1BQAwRzELMAkGA1UEBhMCQ04xITAfBgNVBAoMGFRMQyBDZXJ0aWZpY2F0aW9uIENlbnRlcjEVMBMGA1UEAwwMVGFpZXIgU00yIENBMB4XDTI1MDgyMTA4NDAwNloXDTI2MDgyMTA4NDAwNlowgYIxCzAJBgNVBAYTAkNOMTAwLgYDVQQKDCfph43luobluILpnanlkb3kvKTmrovlhpvkurrlurflpI3ljLvpmaIxMDAuBgNVBAsMJ2UwMTY1YWZmYzU1NjE0ZmU5YjQzODljNmZkZjIwMGNkfHxBRE1JTjEPMA0GA1UEAwwG5byg5Y2OMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEvXfghws+gMv/BXSUbeY3VkOaNQc62ajem6keCkMYVuHQuW1d0naitVEKIh4PHv+CN2JGvQvAQvrS3+wIs5dbWKOCArwwggK4MA4GA1UdDwEB/wQEAwIGwDAJBgNVHRMEAjAAMA4GBiqBHNAeAwQEWFhYWDCBowYDVR0fBIGbMIGYME+gTaBLpEkwRzEVMBMGA1UEAwwMVGFpZXIgU00yIENBMSEwHwYDVQQKDBhUTEMgQ2VydGlmaWNhdGlvbiBDZW50ZXIxCzAJBgNVBAYTAkNOMEWgQ6BBhj9odHRwOi8vbGRhcC5jYS50bGMuY29tLmNuL2Nhd2ViL2NybC9UYWllclNNMkNBL1RhaWVyU00yQ0FfMC5jcmwwHQYDVR0OBBYEFDFLjdWedmegtqqTNzaeW+FWhzYRMB8GA1UdIwQYMBaAFCw9JkPK6QG/rx2xUXNm0U5E2LOhMH8GA1UdIAR4MHYwdAYKKwYBBAGD2EkBATBmMGQGCCsGAQUFBwIBFlhodHRwOi8vd3d3LnRsYy5jb20uY24vRmlsZVN5c1B1Yi9VcGxvYWQvTmV3cy9GaWxlLzIwMjEwOTE1LzYzNzY3MzExMDc2MjcwMjk5MDMxOTQ4MTYucGRmMIGGBggrBgEFBQcBAQR6MHgwOwYIKwYBBQUHMAKGL2h0dHA6Ly9jZXJ0LmNhLnRsYy5jb20uY24vY2FmaWxlL3RhaWVyc20yY2EuY3J0MDkGCCsGAQUFBzABhi1odHRwOi8vb2NzcC5jYS50bGMuY29tLmNuOjE3MDYwL29jc3B3ZWIvb2NzcC8wMQYKKoEchu8yAgEBFwQjDBQxQDEwNTVTRjFOVEV4TURJME1UazVOVEV4TVRnd056VTIwIgYIYIZIAYb4RAIEFgwUU0Y1MTEwMjQxOTk1MTExODA3NTYwMQYKKoEchu8yAgEBAQQjDBQxQDEwNTVTRjFOVEV4TURJME1UazVOVEV4TVRnd056VTIwEQYGKlYLBwEIBAcMFEFETUlOMAwGCCqBHM9VAYN1BQADRwAwRAIgTsohIESpBwnTT6zkmYfgycvkLJR/JIBUk/iesgeMnLACIAz67sgnGcp0owYQkNNK9KGeKQ7szg5XY6yhp/Hk9jzg","hash":null,"timestamp":"MIIFlDADAgEAMIIFiwYKKoEcz1UGAQQCAqCCBXswggV3AgEDMQ4wDAYIKoEcz1UBgxEFADCBxgYLKoZIhvcNAQkQAQSggbYEgbMwgbACAQEGASowMDAMBggqgRzPVQGDEQUABCAd9WVDcZCduOcWLHFdMyk8LuKZDfXqaUGrIhJm6r2awwIIeAiWD+o6AAEYDzIwMjUwODI4MDg0NTI0WgEB/wIIeAiWD+o6AACgTqRMMEoxCzAJBgNVBAYTAkNOMQ8wDQYDVQQKDAZTaWduZXIxFzAVBgNVBAsMDlRpbWV8fFN0YW1waW5nMREwDwYDVQQDDAhNZWRpc2lnbqCCAr8wggK7MIICYqADAgECAhYigTU1A0USAVNhMBBXaDkpgHNickQXMAoGCCqBHM9VAYN1MIGYMRYwFAYDVQQDDA1ITEpDQV9TTTJfU1VCMQ4wDAYDVQQLDAVITEpDQTEzMDEGA1UECgwq6buR6b6Z5rGf55yB5pWw5a2X6K+B5Lmm6K6k6K+B5pyJ6ZmQ5YWs5Y+4MRUwEwYDVQQHDAzlk4jlsJTmu6jluIIxFTATBgNVBAgMDOm7kem+meaxn+ecgTELMAkGA1UEBhMCQ04wHhcNMjQwNDIxMTE0MDUyWhcNMjcwNDAxMTE0MDUyWjBKMQswCQYDVQQGEwJDTjEPMA0GA1UECgwGU2lnbmVyMRcwFQYDVQQLDA5UaW1lfHxTdGFtcGluZzERMA8GA1UEAwwITWVkaXNpZ24wWTATBgcqhkjOPQIBBggqgRzPVQGCLQNCAAQKOqfpOO9IN5w+d/syK0p1x9nN5kA+uY8eCx90pxv3pvieUR9pD/LmLbhlL5w/KGZUM3nU8+pd0e1YdJpQpQtRo4HUMIHRMB0GA1UdDgQWBBRdyNm4aGshfm6TZhyvRPWtX/JDhzALBgNVHQ8EBAMCBsAwHwYDVR0jBBgwFoAUMKhsmrIyB0dDRdSLhcQ7DZU+ff8wFgYDVR0lAQH/BAwwCgYIKwYBBQUHAwgwIgYIYIZIAYb4RAIEFgwUU0Y5MTQ0MDMwMDM1OTk2MTA4NkEwJAYKKoEchu8yAgEBAQQWDBRTRjkxNDQwMzAwMzU5OTYxMDg2QTAgBgYqVgsHAQgEFgwUU0Y5MTQ0MDMwMDM1OTk2MTA4NkEwCgYIKoEcz1UBg3UDRwAwRAIgLqBZZwWG2p3R2insocMeewoJVdEkT4LwDDCqb7ujfhYCIASehhySMz53jrvmJhRVyO1sNpRgsR0v/DVIpe2KNC6MMYIB1DCCAdACAQEwgbMwgZgxFjAUBgNVBAMMDUhMSkNBX1NNMl9TVUIxDjAMBgNVBAsMBUhMSkNBMTMwMQYDVQQKDCrpu5HpvpnmsZ/nnIHmlbDlrZfor4HkuaborqTor4HmnInpmZDlhazlj7gxFTATBgNVBAcMDOWTiOWwlOa7qOW4gjEVMBMGA1UECAwM6buR6b6Z5rGf55yBMQswCQYDVQQGEwJDTgIWIoE1NQNFEgFTYTAQV2g5KYBzYnJEFzAMBggqgRzPVQGDEQUAoIGwMBoGCSqGSIb3DQEJAzENBgsqhkiG9w0BCRABBDAcBgkqhkiG9w0BCQUxDxcNMjUwODI4MDg0NTI0WjAvBgkqhkiG9w0BCQQxIgQgpR2sB4hY3EPWkncjOuABdCQA3z7rBadDeeS0Oa7LwbwwQwYLKoZIhvcNAQkQAi8xNDAyMDAwLjAKBggqgRzPVQGDEQQgQ+r5Yf/8GZYPadH7UCbo+2A7fAT4gT1S3zF/7+EDLcMwDAYIKoEcz1UBg3UFAARGMEQCIBrNQ9HC8UH1wfsLDtuMDBZYUDLlrbUssp06uRp4/Vu2AiAa6PaKuJLm4kMmAbSBZ9gWTNAjLIm2hTjV1yeIoiPMWg=="}}
-----------------------------------
2025-08-28 16:45:46
SignData，hisUnitCode:45038900950011711A6001,appName:OUTPDOCT,deptCode:020101,signType:OutpCnDrugPresc,userId:ADMIN,userId:门诊中药处方
-----------------------------------
2025-08-28 16:45:46
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:45:46
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:45:46
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:45:46
getOauthByUserId，url:http://***********:8091/doctor/api/v1.0/auth/getOauthByUserId?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"userId":"ADMIN"}
-----------------------------------
2025-08-28 16:45:46
getOauthByUserId，code:0;reslut:{"status":"0","message":"success","data":[{"oauthStatus":"1","userId":"ADMIN","userNumExpands":null,"userName":"张华","certDN":"C=CN,O=重庆市革命伤残军人康复医院,OU=e0165affc55614fe9b4389c6fdf200cd||ADMIN,CN=张华","certSN":"1002020325082116d28bc990349cf0169857","certStartTime":"2025-08-21 16:40:06","certEndTime":"2026-08-21 16:40:06","oauthSignature":null,"authKEY":"********************************","authTime":300,"expireTime":15337,"authStartTime":"2025-08-28 16:01:23","authEndTime":"2025-08-28 21:01:23","idcard":"511024199511180756","transactionId":"yxnwm4d2oqcr5d5w","userPhone":"18010547730","officeName":"测试病区","callbackURL":"","authType":"1","oauthMethod":"10","optUserId":"ADMIN","signatureImg":"iVBORw0KGgoAAAANSUhEUgAAAsEAAAEFCAYAAAACIDpLAAAm5ElEQVR42u3d66tmZf348dKcMUMbO+ABj0hmlo7UAwtrZgvZgJkzz0wlZ4JCIWrmgZCP5lCRgZAbUqEkHUGwiJoplSho3O4oehLOSI86uenwd9zfLn+/u27vudf5dF1rvb7wpq+6973Xfa1rreu9PutzfT5vm81m5wAAAABT4m0GAQAAACQYAAAAIMEAAAAACQYAAABIMAAAAECCAQAAABIMAAAAkGAAAACABAMAAAAkGAAAACDBAAAAAAkGAAAASDAAAABAggEAAAASDAAAAJBgAAAAkGAAAACABAMAAAAkGAAAACDBAAAAAAkGAAAASDAAAEB3MvO2t80WefHFF2fGBSQYAACMkh//+Mdvkd+3v/3t//3/zznnHCIMEgwAAMbF1tbWbDkCvMx5551HhEGCAQDAeFMgsjhz5gwRBgkGAADTEeA5xgwkGAAATEqASTBIMAAAmJwAk2CQYAAAMDkBJsEgwQAAYDICPC+Xdt9995FgkGAAADCdCPC2bdsIMEgwAACYjgB/6lOfIsAgwQAAYDoCvH//fgIMEgwAOJuDBw+eJQ5vvPEGcUDyAnz77bebxyDBAIBqgvHaa68RCCQrwJ/73OfMX5BgAEA9wTBOKGLHjh1nzZvjx4/PhhRgKRAgwQCAlRw4cEBdVXT+IFX3c2+55RYCDBIMoDmvvPLKbI5cT8zlZV47lQijDkePHu1k7oQ0HAIMEgx0GLWoIgBtMcTfbMLu3btnjz/+uEVFriURRu05FO4jbX1u3j2UAIMEA0uEyGdK4jm/yacizDZPTUeC9+7d61yj1vzpOg/4tttuMzdBgoGUhHcMLMu6dItxXyfOLy699NJO3iLUnZPHjh0zJ0GCMU2uvvrq0aUhdJVu0dc4iA6PW4SNnXnT9pypOxdPnDhhPoIEY5qsra2R3/+wa9euN/PuAkeOHHkLGxsbs8WNceGf5/8t5NCF3+linMxPIgzzpUyubt2HfOcDJBgqH/xH7BZ3KaeQUxvEM+RXziU19shpVrQ9b4zNzfhpUobKOZ4eZe6tq/5b3mcu1hguc8+e/4zzARIM5PDiiy++KZldiuw88vrMM8+8GWUVBfofUxiPMZzPJg+LduNPh62trdYflMJDWJ3553yABAMtSdxcZElbuyL89a9/3XgmdC6X/7msnMj/dt1nceWVV87y3uApgQYSDCAZqpSbe/e7323BipjF19CLm4ukRaDs3ossYS2TslB1joW3bc4FSDCAJKJB99xzj0UroShw2fO8LD5yNF33VR+Mqn7WY489Zn6BBANIZzE0XulE9NvaNGdsp3XNZ0WC87pMVi35+OSTT5pXIMEAhuXQoUOEaIRik9f84uDBg0TYXGltDlx++eWVyqA9++yz5hNIMIDhqbqL25ilmQqxTNj4VkVc5IIT4FWErm6qy4AEAxjtghhkKHDrrbdawCJlsaZ2lYeVKgKzc+dO5z9xrrnmmtY6RW5ubnqABgkGkCYnT56sFA00ZumnQuT9bpm3AiGVwnhPJwqcV76MAIMEA5jEgnjZZZdZyEaSClH0+0UyvFh6DeMV4IsuuihXgMumUhl7kGAAUfHUU095lTnSV9yqhKAoXabJOTZPQIIBjDoqJA1iGqkQZVIjsiJ+xn58UeCi+tBlP+fqq682P0CCAcRHlVqxY4yWhkoHTWUxFsrUBu5qsyQRToPFLoJNrvkLLrjABkqQYADTiAqFV6hj/65j+35D5I+6psZRASb87x133LHyfD766KOlcoAJMEgwgFFsjpnKd53yhjhzxfXeVh4wAQYJBpDkorgc5ZmSAOSVgYqZcNxdnzMinC5XXXVVbwJsvEGCAUQvhXmvNOf/7aGHHkp+Uau6Gz51yc8rZ0WEPfASYJBgABbFiSxqa2trk5Lgvv5WmbxQ1xoBBkgwAAIcQTWIIsJrYxviRIQJ8P+48cYbnWOQYADps1xGa8x5wHOqlIUKJdNSFp0+a7IS4bgJc6FpLvyrr77q3IIEA5hOZOiVV16ZTe07z/nwhz+c1Hc/cODAoEJChNPPg9+2bVvtNAhjDRIMIAnKNMbYu3fvbIrin2p1iBgaVpQd2+3bt5OmHtja2uqlEoSxBgkGkLQMLqZBpJgK0LYE7969O5kxWE5tOXjw4Cz2MX7ve99LngY+F2XaoBNgkGAAk1gYx972tooEb2xszFL8XuEc9vk3V+Uelx3jG264gURFMNez8scJMEgwgFEujlllrYhBeuOweC67rmpx++23lxqzMpsuA3fddReZGnCeZ831opQp4wwSDGBU1RHG/v3zBC7Vsej7uIvG7bXXXptVfR3/rW99i1QNJMAnTpw4a+zDOcx7gNnc3HS+QIIBpMOzzz47mhzYPnbKk+DiqHObEeFf/epXxKpnAc6aL3k/P2S+OUCCAXSyQE5hDA4dOjQqCV7ugPf444/PYhCtxcoa//73v3MFePGf//KXvxCsSAU4nKexbpgFCQYw8gUyK4L38ssvT2Zhq9I2WRR49UNEmUjw8rH8+c9/VkO4Q9bX1ysL8Koa4PKAQYIBjIrQKjhLXKaQArHII488MhoZC6kdfXf3a1JjeTHPtEikXbf9R4GLagobZ5BgAEmxaoPLlBe2MjnBqZSJG+J4y9SbzWvX/Lvf/U5EuGXCg2yZB4smecDGGSQYwGgiRH3Vko2NUPt3DI0ywqvsss0O2mK5LXPe2AW+8pWvzPKOnQifvXF1165dZ43BZZddltu+vEpDjKy5nffz7qMgwQBGIcBjb4bR1qvjPOmI8TsEgYrxlXsgpOMsf9ZLL71UWtrGPBff+c53NhrPNtIgbrrpJg8hIMEApiF6xibtCOSqFJdY807nQrtYO3jOCy+8MMmI8HKL67o8/PDDlX/n3nvvnZU9r+6jIMEAkiNsSqpSGJ8EpyUAQxxvG9K26nN/8IMfTEaEQ4OJMvWSqz5gNDkHeYLtXgoSDGA0G+H27t1rYRuhBHf99w4fPtxK5DLrWL/3ve+V+t3t27cnO3+vvfbaxtLb9tgvlkOTBwwSDGC0gqfI/TgkuMtjPXLkSGcCNpesrDcRjz76aClJzKs8EfM5K/peXchx3qbJvJSMPhquACQYgDxgEtzo2IPMxCa8datuzEW4iLvuums2prnWpuwu/3PWw697BUiwQQAIsLFKpjxamQjfsuju2LGj02hjG+kQi3znO98p9TkhhSKlzW9DjH3WPHavAEgwMGqpUwqtvgTHunlw+TiD5IZjnTdLSIEy37NsRHhVxYmUIsCrxLgtWV7VsS+Q1155qrXDQYIBJCx0WQun8aknKGN9rb44T/quSlD14eLb3/52shUjYnjYOHPmjCgwQIKBacrc1taWRS1RCQ6NOlZ1DutiU1WIIoeI8qqoalHDhirHsXPnzsrjWjYiTICblUMjwSDBAJJinuu5SjyMTzoSHDq99SFEa2trlbrgHT9+vHB8ym7QajI+KYlwXqpBlYeWgwcPvrnhMXDnnXfWfuBZ3jSZ9xnuCyDBAAhwi9HMOcv/fugSTEMJ1enTp2eLtVnrRHHL/Hwb9aDLRnSzfu5d73pXa+OYigg3OYcf+chHZnnXetNosCgwQIKB5FmWqKEWshBlOnr0aOv1S4eWlawNRXXJa17SVmpD2xv5nnvuuUpzbF5urcsavuHBKWYR7mKz4PLcadIhjgADJBggwDVe1+dVH2hrN3sswlIlZSCLEIlts+5rLC2S2xibJjz11FNRdpUrc1x18qTbuK5CneCi+eXeChIMIAkB7jIFIi8PNJbyWW2QJ5l1Uz/aeCAo8/tdj83Jkyejjhg+//zzhWN0/fXXR/NQlcXtt9/eqQCXnYtDP9gAJBhALvv27etETMq+Yi5aaNtsBDC0tJT9jC5q8y7m8mY9kAw1NnU703XBT37yk//Ou6y5d//99/dyvPMHoCpcccUVs7oPaak+eAIkGMCgKRDh9Wss5ZuyFvqLLrpoFqMEh1zhtiNyefnHQwjLsngPnbudx89//vPCsQ9R7T7mUtWmF3mfF6p4lHlYauMaDKlO7rEgwQCirgLRRIjK7C7vM/IUwyv/sn97sRZu0zEKZa/KHl+WCA01LrFeH7/85S8Lx73retltbwQt2wlOFBggwYAyaA2rEaxqnvDMM880WiDDq/PbbrutthDXaazQRvRu3gSiqfCGKglNci2HEJa81/oxXye/+c1vBpO9qvPi3HPPndV96xN44IEHZm2lMt17770kGCQYQIQXZ8UFvY74hrJWfX2fkANZRYTDv4u1nNWq3fdFJcqCYJaNSMYWHU/hevntb3/bKAWhr3mU91kHDhyoXElCKgRAgoFRcf75568UxCBby9HWKote2/Vv2xKGLHGJVYJDpLzLiK4ocD3+8Ic/9B4Rrvp2IOtziroGLl/7eX+/ylsM91uQYADRR4AXBbZKB6mYoj15C/TyLv+u0iHqvEKuGzGvIx9DRoHH0Er3j3/8Y2/it5gv3mZDjGXyuitWPYZldu3aRYRBggHEK8BzkS27AWd9fX2WynfL4umnn54N8fff//73z7r4e2UEM7wOX/65rtNC8t4oZEUfY+f111/vRYTbSoVocpwbGxutpPO4/4IEA4hOEuftaGNPdWgiDF1FIEM1hiF2y5dtJb0cYa6zkWrqucBZ/PWvf50VvXnoYtyy/t7m5mZuOsOq3wsR4r7y2zXMAAkG0DunT59uVIEghe9Yp9LCUPm+fUcIYxDRVRJ21VVXJS9FRTnzl1566azv81w2PSgvBaKrTZ7uxyDBAHqjaDd41gKZUtSmr8W4bK5vHzmwed39VtFGLWhR4NX885//zB37T3/607W+60033dRoTletAtFXpZPwUO7eDBIMoFMuvvji6BpIdCH5XUeBlwVy1d9bFVXreoxTbh9dpfrFGB7GvvGNb8y6Pr9VfneIuZbVuhvtslgFJPYUNhIMYPDoaCopD02+52JliDKfG1rhFsl1UevlPsQzdgme2mvxvLE+depUZ9HXPXv2zLoS4C4kWHpEu2zbts04k2AAVRasQ4cOzaYi+mUXg/vvv7+1knB9RMCKar8O3chkVfR87CWz8lKO/vGPf3SShztPMehKOrtsfz7GtwJdE+4jHjhIMIAaQjSGXdpV6hiXWQQuvPDCWZevkOfi0MXYF7XBHWpRDG8YproYt1GVpOq57PJ8B1HtKhqsekQxL7/8cuV9HcaXBAOivyOUj7zOY1lklYIqSqEIJdDaPCepbw5sekxPPvnkbGrX5HKTlrJj30Qo6/y9PNbW1mZdRoNFhtt72BcNJsHAJMiKzrS9AKaeBnHddde9ZQx+8Ytf1JbmWCW46rh01S1vTtiQM4W52OSc9JWH28ZcXpbgPukjbSellBoSTIKBSVOmjelYOzQ16VT1hS98oTB61vWxxjQ+bchRleOY6uvYulJSJD5lxKitluaLpRa7jAKXaXdepb7x2CO/Vc+F9ZMEA6OW36nuvM8bi+VScct84hOfmPV1vLGNUxd///jx4xbgFkS4qXC2WR7rkUceKS1doWpKE5Gr+p1D98Sp3NtFgkkwQH4nFv0NrK+vt5Ib2Wd+6lCLUFGDlK6PadXfCJ3V5O1nz81VP799+/baEcC2012qpi8UddKTRtFuyst8PhTNC+spCQZG+Wrs2LFjs6lG0qpKwpDHHOPcafPhKUt+XM/583hVKs5TTz1V6/V3Fw/Ddas8dPFKv+5muyDFQ6fkhI22bY2FVAgSDBC+Cdzc2lgIQlRt6OPue9d7lTnUVu6oxbf+eVklr3XmftvH+8UvfrGz9s2xER4e58J84sSJStIcfjb8XqjhGz6jjZzutmR4ipsLSTAwoijREDv8Y6Do1X7RAnD33XfPYjmfsT9QkeB++Ne//lW6kkkQsaEfiNt6KO9baPuUzBg/P9Tp3tracv2RYGCcG76MTTbPP//8LLZjT2EMCfDwIvyZz3xmVvX8tV3ZpOs3UyEymVKEuAvJXfXvmwpxlxVfQIKBzheXvJvg/L8R4DQeDmI5tpAvXnaxbpJPSoKrEaJ0Wdd8qMaw+LPnnnvuIAJcp2pB3b+1sbExe8c73jFIznDMspxHk2Y+IMFAUtHNMdfHXCbIWNlxCYKQwnke8liqtFYOJc4IcD/87W9/yxShn/70p7Ohx7mOrLV9DKdOnXozpzXk2a7a8Fkl0po6bZa9AwkGepeiujfmKY1TXh5kSh3IYjuHXc43ElyfP/3pT5nn4fXXXx9sHE+fPl1LKmOIsAdpXt7sGVIFhsjNrfO5Qfin2lyGBAMTLnU2dEWBlCLlsee/xXgeuxBhAtyc3//+99GlP9W5X+3atSvac59Xu3hRVMOGsjKfN28N3kYTEBFeEgyMjs9+9rONogHkN10BDvmNsZYoKrswh85fdc7bmJu2dMmvf/3rqO4FdSKaMTdFaeveWyW1KE+QiS8JBkZJ0Wu3ooVlCqXPmkScUtgBHVq5ZjUQSGnMi+oHe3hrl5/97GdRiPC+fftGl7ZV5Xtkyfzm5mYj+Q2pXuY5CQZUfOh5l3esrybH2vVobW0t6uMOctvGmEvfaZ/nnntu8PtDXckbiwQv/27dkm6qN4AEQ96v3N9SeXljSg9J4dirvNYtO+fdC9rhiSeeGPQ6GNs1WvW+M/+9qiXirrrqKtcASDCmw3LET+WH1YR0gCaF5FN/IzDG6Njyf19fXycALfLwww8Pcs8YYwnH888/v1H1hqJNbjqzgQRjUlR5nRxjS9+h5XfsDwkpfYewoJcRgcVc9bCDXhS4e7785S/3fl1MPRWiSJD37t37Zs6wEmYgwZD3K/qb+5CQJVdFOXapd8dL7XyXjYjNNyWmVBUgdT7/+c/3dh85c+bMbOwSXLceMOkFCQb5bUiIIkw5Qj6vNjD2B4XUvsuqiiZZsiAK3D933HFHL9fJGB/sm+xDMLdBgjF56pYLmtLNNMhtXoRlsdTWFMYqxe/zyU9+0ryOmI9//OOdn4O697aYm2RkbVrOu1+VbZgBkGCI/v7/G2req/+pR36rVNEY27xJqXlEnSon7hP98YEPfGDlOTjvvPNmQ0pwzPVvq26IM89AgjFp6taEnNINtar8Bg4cOFC4GI3x4Sm1WqHmd9xcfvnljTr8TSkVour3Cm/9zDGQYIj8NmSsG4WKuuFldRgr00VvLGO03DI5xQ01JDhugvCueohsUqd2bBIc2hILWoAEAyXIKopedSfxzTffPJviA0Kd9rpjqAJR5k3C2B8I3T+GYfv27SvPx8c+9rHK5+T48eONSojFNC4PPPBA7RJo5hVIMCZFKHIu9aG+DJXJd55KCsSc2Fsm1z33eQ+E7iXDkHVOqtYgH8O97/Dhw42+w5gDGCDBQGepD2PtD3/ttdc2bvM8xRajY5L8sk1hjh07RiAiu4999atfnfVxLwxpB0N+/7Apr27tXw9yIMGQ+uDGWXpjYJVKBzEvnH1KyVQeFt1b4jtHTzzxxKxrCR7qO8/r/+YJ8COPPEKCQYKBwPr6emvyO8buQUWpIW2J07zrGAkejwSPMbd7LOfp5MmTs7FJcNmNyVVaIZtDIMGQ+lCQ/xj+/dTGp+pnvfTSS5OOtoy5813RtTLW6yP18zUWCa5alUcUGCQYUh+kPmSS1xyhqOJD2wsxCR5P3rx7T1znrKvz3FcjmMX71KoARVa023wFCYbFwMa33J3Uy4tK3RaoebWApzrvym4gTOH6mXcFi0mOUHzuyvz8ueeeG+XDzhVXXDFrUp2GBIMEY3Isl6hqkgYh9aFZFYEHH3xwNmUBOXr0aJLfP+S8582NMtdSyg8AggbDyuOq+Vd1boXocJnv8Pjjj5unIMFIn/luYakPq9m2bVsn3zcsIqIrqzdfpto5sOhcBrkvcx3VTalBvBLc9SbItt4w5KV6iQKDBEPqQ83mD2Man6aScuDAAQtLzluIFL/DKsH1qtl9tOtz2vbfND9BguGmPfHob8hnXozezP/3xhtvbPx9b7nlFovKCDfFVfkORGN699S2/96+ffs6+XtlG2g4x65TEozkuOeeeyqXN5t6u+M2X2OGV42rxplApD0e82oq83Nb5vUzER4f73vf+846f5dffnmr57Ao77dpKlGZOTn2muVVUuJcpyQYE4v+liki70lfFGFKElzn+POqgpgjqHIPCQ9fbaWklQmSTGW8Q4t6gSESjAkK8Kpo5YUXXjjaizyrMkZbO6DdNMcrwcsbifbs2VP6+LNSY8wVlN2s1nZZvanPxayKPWXWTHOVBCMyil6dWYC7F9QplZGbogR3vRFpihE4/D/279/f6325THm0sW2CDt+nSVqgNtIkGJESNnE1aXUceM973jM5AW4zsiKqV32cUqoR3FY+pgdSLFJUtjIEN/p8IzaW+RfqJLe5Kdz1SYIRKUHkml7MY65VmnWzb3NxcZMsx8bGRi8LfB9vWdqeL6seUMOrcfNmmqlrXTdSGYvkhbWrbL1jEkyCIQd4UgLc9c0rK6dMS9zVLNdMnnIKR9k3NubS+JhXGBlSrorWhZ07d0Y372699dZBZFe3RxKMxKKcZdMixlz+pms5nY/9cgTvyiuvdHMcUT7wfEPb/Dy3JQfLnfPy+NCHPmROTWDfRkzBkyHTQ4ryo4tS+1b9tyb5v6LAJBgjk+ApCnCbEW81NacjwV0ec9XXuObQON/YDbEBbei5Fh4IwgNlLJHdKqJ8wQUXuBZJMGLg9OnTtZ5wp7bYpJRiQYLjPd4ucpirLMhSI9JjVWm8Ko1W+sjN7/J+duLEiVmZ8oB11rEqvzP/2ZDGEDbkPvjgg28+fIRrOvzvfCPd9u3bcz8n/J55TYIR4UJd9vUQASbAJLjerv0Y8vrDq2JzSepDE4raMNdJ+QlivaoiQ9tpCEV5um1F1cNbvXCttVU/HiQYHVG2G9X1118/m9oY9DHG5mA92Yt9Y0nf57nKYt+0XS6GO5cxbER+9NFHawt6kMyuqjFkCXPYSDi2msUgwWiRSy65JPfmsrm5OdobSFZKSNdRkxh3T6ckBzFHWJZz7a+++urOj7Vq9yrzKT7CPMk6X33MobZScMLPhJSBviK4IS2E5JJgoBEf/OAHJ7dYdh0BnmJZub7yEEWBzyZUgSDC6RHyXlM6T12lK+R91q5du4guSDC6J9xsrrvuutmZM2cIsPzfaLjhhhuSGMe+Ona1kRZhPsY3XxZFMMhxLPnJXXdQC58vfxYkGBhQFOT/liNEZPreeJXCWPa5GY4Ip00okxVj6kOZGrt1I8FBdFPp8ggSDIySrBzdNj57uZPT0KWM6rK1tTU7fPjw7LbbboumGHwK4rYqNzLGzVVEeNqpDyEda1l22yoxtnfvXqILEgykEqnruo1tTBtaljl27NhscTNOzF2RYpe2j370o2fVE425yoAawnGdj65SH5p2T6tCzPc6kGDAxdKBqIU8tlQ6wHW1YztrEW373MUaWV8s+RRDFLiuCKtY0g3Lb4i66B4W7jVt5u1mVVzI6zKq9B5IMJDQBpTw6q4rwYhlQ0uXkZ8+o8FDt4nNY3k8n3766VnsD3+aaaSd+tCW8IbzXeVhPe8e4pyDBAMRklWcfYwC/P3vf7/TCG/ZzoJtjkXM5dFSya8t06Bgfv6U8Ov2waNqFYQ9e/a0ktKwvr4+6yKYQIJBgoFIyUoBqPt5ea8Dm0aWu0jJ6Cvq22VKxPKYxyyWQ82BMmzbtq30+STC3TxstJHHW+bhNNz3+pZ75x8kGEggIlM3VzdvAeg7/zdPfPvYBNNnOkSMi23XdaZjEGF1W6tVXKh7HTRNbehzE2bfG/sAEgy0eMOus6kqK7e2b/Ep81q0yUIa0g6KjiGvRvCUJDjlSFiIrJd9GCI3zR6OV+VY33LLLY2u1SEfTrIeps0DkGAggYVpiNebTTh48GDrwtvkVbdIcHsPVzFv2vKqu717Q9VI77Jglnk47YPl3HzzAyQYiJCsSEuVz8iqKTznvvvu6+zGn/dqtWo6w9133z2b9RAJmooEd9lsJfbKEbFV5RiKolSkO+64o5bwhv8/PEydPn06ynHOmvskGCQYiISwgDQt5D5E9PfOO+9sZVNal/mBMZRHC5HxmKQxjH3qKQNVH7K8YWqX2OqJV/3uN998MwkGCQZiXtCb3ujbboKQtbEq1i5NQ22Ke/XVV98y/kMKw5ilcKzi1hYXX3xxFOlIMc4RbZJBgoHE84AvvfTS3LSDNkpffe1rX2tlET1+/Hivi07RxsAuxXD5FSwB7o7Nzc1BW2PHRtl8/KLo+aFDh2ZTv78CJBgYIF+taMNSGcFr0g60jYoOfZZAaiNK2Gat3BgW3Kwc86HPS0xR/9Rb5objL1O1oUy6yNTmhfUHJBhI8AbdVf7jNddc00h6g7j//e9/n6UqRF1uxOv7++elrch9TXdMwsNvyP3PugdU3YCa+kNAnVSzoa5JgAQDJRbsU6dOzapEfxcXviq5tnXye1ctsrHlWWZFxvIEoetzK/rVX2Q01TcXVR9OmzSQ2blz5yQkcKoPgyDBQNT85/8q3Zzb2vTTVv3eWDfJLC96ZUShiw0yQy64eZF61RCKef3112exilsT+Y25NXZXZLWfH2vqB0gwMKo0iLLNAepERqsSNsilNq5Fr427iogNVR4tL6XFNRZvikTYNNpFGbNVnd+mxGLDj8V7wdSqhIAEA9EvzsttRess1mHRe+ihh2ZFNYPH2GggFtFZ7lDV1/f/0Y9+RIBbFuEux+7kyZOdiO+UIv7SgkCCgYRYX18vXGzL5uoG4a3z+r+IZRlPgTrS39WxrK2tDbLgDl2TOUURLnu9tCWW4ZptmoefdcypNz/p6tyvGi9jAxIMRBiZ6CIyVIahO5p19WAxRCRoiMoQfTRKGXNEuKwMP/nkk5XHs2nVFZ3w2k+PMjYgwUAkUYlA2MBRNYrbNOo7lnzBqmPR98I7pOC57tpNjSg7rmXfTDS55p1T6RAgwUASbG1ttZqyUGdhDf9+bDvEq47pEAvvUGKXapvbVER4+dyGa6uPtzYp5enHeL6NH0gwkPjiW0X8xloaqep49bUjvK9STCFPVR5w85ztNq6/rh9sndP27hHGBSQYiGTRnS+eXSyiXdS/jYV52beYIsCB5WYNXZ2Dotftrrt86jSKKSvFXVzLzhkJBgkGRIFHWt2hCgcOHIhWIA4dOtT53y6q++yai/faJL8kGCDBsMi2vNFtKkXxQ55rzBLR9d8vegBwzfWfHiHyGyfL9bqNLUgwMNBmuK4W1CmN5/PPPx/92HR5DEUCrBVsfV544YXecvXzcolDOo3z0Q779u1TVQMkGBjbq9auWv2OJYdzyA1EXUlwmRQQ11y66RHkt31W5WiPdZMwSDAQFUUNHOq8Qp1qyatQ0iiVMeqiy1iZFBDXXNyb5rKu+zFvXo3xgcZ4gwQDiUWUpjyOVTbBxXbeH3744cbHFFrhFkmV6639c9h1yTMyZlMcSLCBgOoFGZGib37zmzPjeKBwrGJqgxq6/rW54GqcML50CPI77IZH4wISDHRIUfmqvHSItl6fj1GA8yJzMQpU0whtmTlkvrTLYu3lvK6LddKayO+wEf2YHpYBEgyRJBGKxgJ8zjnnzGI+913PIXMl3ijwfM7aiCUdAiDBmFwkqSyhNJOxqx5Jj7EhSBsLLgGOU4Kr5ghrcRxfbrc3JyDBwICRJGPUjgDHeOzL7ZLrHCcBTmP+5Ylx2MhoLOO8H0+loRBIMNA711xzjUYGPezIT+n8l/3dvJJcy2MimtWvOJWNABu7+M+lBxSQYEAUOMlczNgXsLrnvGolEfOlW6rWCDZm6VyTxgQkGBhI4oxT/tilUP2hyhwoE/m3eTLN69lbHRIMkGBAFLjTcYtx81vZ71JUEosAp3M+t2/fPtuzZ49zQIIBEgyIAntwWGRVi+ym0W+1TQESDBIMRMvx48eVSqpI2KGdN147d+5MbryqbIoTAQZIMECCMYmKBsapvACGMmNj+V7LPxPSI6pWHggPWeYNQIJBgoGomO/qJ8HFhMoOeQI4Rrlf/O+L9WfLltxKMSIOkGCABENUU03XEuOU0sa3LLa2tjIfflZFf8tGg11jAAkGCQai40tf+pJ8zhrR3zGOzYUXXli5VbZ5A/QnwTaYggQDPUeBp3zTzRuTvXv3zsb6XcumOpgzQP/XpjEBCQYaEjZvSYVYTUhvmJrgiQAD0iEAEozJRjlXRQCNy/84ePAgASbAAAkGSDDGHgWe0g138SFgSg8DP/zhDwkwkIgEr62tuc5AgoGuI39BBKdQ3qpoHI4cOTLaMZiXx6tS7YEAA8Pdo3bv3u1aAwkG6lKm3NUUxGZVd7SpiZ0IMBA3Gxsbb7nWHnjgAdcbSDDQhfgsR//G+P1DdJvUnb24EmAg/ut0rPsSQIKB6KJ/U4v8hij5VOZBSPNoKsCvvfaaBRnoUYLHnJ4FEgyQ4JbZsWPHbMp5v21GghffFBBggAQDJBijleBURads9YupLyhSIIA0JHj+AHr48GHXH0gw0EVFgNRFuEzKA/mtL8Fj65IHpBYJDm3cjQtIMNCj/KSe7kB+q80FTVOAOCV4SvsWQIKBaF6Dp5bqsMhjjz1m4chh//79mWO3ublp7ICBePXVV0kwSDDQJuG1dt180CChMUma3NX2COWXwrhdcsklxg4QCQZIMESD+8wPnYtYXYIwO78ASDBAgoFSIly1TW4Wobd9aO25Z8+eN3NwA+GfA3Vb85b5OecTwNjv0yQYJBjoaWNUW2LcRG7z+O53v2tBADCZe/SpU6fc80CCgZhSI7qIKGcRIszOGQCRYIAEA61VXbj11lsHleFVv6M2LQASLO0LJBjojRBx6EN+l38+bIwz/gBAgkGCgejkuK2IcRBer/cAIF+C58EC4wESDAAASDBAggEAAAkGSDAAAJATDJBgAACQtgQP0bYeIMEAAGBQCbaRGCQYAACQYIAEAwAAEgyQYAAAQIIBEgwAAEgwQIIBAAAJBkgwAAAgwQAJBgAAEUjwvGPciRMnSDBIMAAAmFYk+MiRIyQYJBgAAJBggAQDAIBRS/Czzz5LgkGCAQDAtHKCjQdIMAAAmFwk+I033iDCIMEAAGBaEqxEGkgwAAAgwQAJBgAAY5dg1SFAggEAAAkGSDAAABgja2tr/5Xg/fv3k2CQYAAAMC0J3r17NwkGCQYAAOMniC8JBgkGAACT4ujRo7PFvGBjAhIMAABIMECCAQAACQZIMAAASJzQIIMEgwQDAIBJsbGxQYJBggEAAAkGSDAAABi/aJBgkGAAAECCARIMAABIMECCAQAACQb64P8AMub5f8I40p8AAAAASUVORK5CYII=","imageUpdateTime":"2024-08-20 16:59:35","sign":null,"officeQyId":""}]}
-----------------------------------
2025-08-28 16:45:46
SignData，getSignData:OutpCnDrugPresc,data: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
-----------------------------------
2025-08-28 16:45:46
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:45:46
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:45:46
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:45:46
signData，url:http://***********:8091/doctor/api/v1.0/sign/signdata?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"userId":"ADMIN","transactionId":"OutpCnDrugPresc|0000000071|11014386|250717000ADMIN00001|6|1","authKEY":"********************************","fileName":"门诊中药处方","data":"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","isHash":"0","timestamp":"1","isBackTimestamp":"1","isBackSignatureImg":"1","isBackSignCert":"1"}
-----------------------------------
2025-08-28 16:45:46
signData，code:0;reslut:{"status":"0","message":"success","data":{"uniqueIdent":null,"fileCode":"202508-aefdf74b09664064a1a1374e1ef04b915","fileName":"门诊中药处方","signedData":"MEUCIGU38myvu7Uz3ZrAbSUQ6hPt8sskkl/GhHgEuLL/+7fKAiEAprwxwkzxEkqmGmE3xP7z7RAzX6EIPMqEWUBiv188pmU=","signTime":"2025-08-28 16:45:45","certSN":"1002020325082116d28bc990349cf0169857","certDN":"C=CN,O=重庆市革命伤残军人康复医院,OU=e0165affc55614fe9b4389c6fdf200cd||ADMIN,CN=张华","certStartTime":"2025-08-21 16:40:06","certEndTime":"2026-08-21 16:40:06","certIssuer":"Taier SM2 CA","signatureImg":"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","imageUpdateTime":"2024-08-20 16:59:35","signCert":"MIIEizCCBDCgAwIBAgISEAICAyUIIRbSi8mQNJzwFphXMAwGCCqBHM9VAYN1BQAwRzELMAkGA1UEBhMCQ04xITAfBgNVBAoMGFRMQyBDZXJ0aWZpY2F0aW9uIENlbnRlcjEVMBMGA1UEAwwMVGFpZXIgU00yIENBMB4XDTI1MDgyMTA4NDAwNloXDTI2MDgyMTA4NDAwNlowgYIxCzAJBgNVBAYTAkNOMTAwLgYDVQQKDCfph43luobluILpnanlkb3kvKTmrovlhpvkurrlurflpI3ljLvpmaIxMDAuBgNVBAsMJ2UwMTY1YWZmYzU1NjE0ZmU5YjQzODljNmZkZjIwMGNkfHxBRE1JTjEPMA0GA1UEAwwG5byg5Y2OMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEvXfghws+gMv/BXSUbeY3VkOaNQc62ajem6keCkMYVuHQuW1d0naitVEKIh4PHv+CN2JGvQvAQvrS3+wIs5dbWKOCArwwggK4MA4GA1UdDwEB/wQEAwIGwDAJBgNVHRMEAjAAMA4GBiqBHNAeAwQEWFhYWDCBowYDVR0fBIGbMIGYME+gTaBLpEkwRzEVMBMGA1UEAwwMVGFpZXIgU00yIENBMSEwHwYDVQQKDBhUTEMgQ2VydGlmaWNhdGlvbiBDZW50ZXIxCzAJBgNVBAYTAkNOMEWgQ6BBhj9odHRwOi8vbGRhcC5jYS50bGMuY29tLmNuL2Nhd2ViL2NybC9UYWllclNNMkNBL1RhaWVyU00yQ0FfMC5jcmwwHQYDVR0OBBYEFDFLjdWedmegtqqTNzaeW+FWhzYRMB8GA1UdIwQYMBaAFCw9JkPK6QG/rx2xUXNm0U5E2LOhMH8GA1UdIAR4MHYwdAYKKwYBBAGD2EkBATBmMGQGCCsGAQUFBwIBFlhodHRwOi8vd3d3LnRsYy5jb20uY24vRmlsZVN5c1B1Yi9VcGxvYWQvTmV3cy9GaWxlLzIwMjEwOTE1LzYzNzY3MzExMDc2MjcwMjk5MDMxOTQ4MTYucGRmMIGGBggrBgEFBQcBAQR6MHgwOwYIKwYBBQUHMAKGL2h0dHA6Ly9jZXJ0LmNhLnRsYy5jb20uY24vY2FmaWxlL3RhaWVyc20yY2EuY3J0MDkGCCsGAQUFBzABhi1odHRwOi8vb2NzcC5jYS50bGMuY29tLmNuOjE3MDYwL29jc3B3ZWIvb2NzcC8wMQYKKoEchu8yAgEBFwQjDBQxQDEwNTVTRjFOVEV4TURJME1UazVOVEV4TVRnd056VTIwIgYIYIZIAYb4RAIEFgwUU0Y1MTEwMjQxOTk1MTExODA3NTYwMQYKKoEchu8yAgEBAQQjDBQxQDEwNTVTRjFOVEV4TURJME1UazVOVEV4TVRnd056VTIwEQYGKlYLBwEIBAcMFEFETUlOMAwGCCqBHM9VAYN1BQADRwAwRAIgTsohIESpBwnTT6zkmYfgycvkLJR/JIBUk/iesgeMnLACIAz67sgnGcp0owYQkNNK9KGeKQ7szg5XY6yhp/Hk9jzg","hash":null,"timestamp":"MIIFlDADAgEAMIIFiwYKKoEcz1UGAQQCAqCCBXswggV3AgEDMQ4wDAYIKoEcz1UBgxEFADCBxgYLKoZIhvcNAQkQAQSggbYEgbMwgbACAQEGASowMDAMBggqgRzPVQGDEQUABCCWsl8dHbQEAfYqILDjV3jiTmLqpGecirnVRjbSfi4P3gIIeAiWJLB6AAEYDzIwMjUwODI4MDg0NTQ1WgEB/wIIeAiWJLB6AACgTqRMMEoxCzAJBgNVBAYTAkNOMQ8wDQYDVQQKDAZTaWduZXIxFzAVBgNVBAsMDlRpbWV8fFN0YW1waW5nMREwDwYDVQQDDAhNZWRpc2lnbqCCAr8wggK7MIICYqADAgECAhYigTU1A0USAVNhMBBXaDkpgHNickQXMAoGCCqBHM9VAYN1MIGYMRYwFAYDVQQDDA1ITEpDQV9TTTJfU1VCMQ4wDAYDVQQLDAVITEpDQTEzMDEGA1UECgwq6buR6b6Z5rGf55yB5pWw5a2X6K+B5Lmm6K6k6K+B5pyJ6ZmQ5YWs5Y+4MRUwEwYDVQQHDAzlk4jlsJTmu6jluIIxFTATBgNVBAgMDOm7kem+meaxn+ecgTELMAkGA1UEBhMCQ04wHhcNMjQwNDIxMTE0MDUyWhcNMjcwNDAxMTE0MDUyWjBKMQswCQYDVQQGEwJDTjEPMA0GA1UECgwGU2lnbmVyMRcwFQYDVQQLDA5UaW1lfHxTdGFtcGluZzERMA8GA1UEAwwITWVkaXNpZ24wWTATBgcqhkjOPQIBBggqgRzPVQGCLQNCAAQKOqfpOO9IN5w+d/syK0p1x9nN5kA+uY8eCx90pxv3pvieUR9pD/LmLbhlL5w/KGZUM3nU8+pd0e1YdJpQpQtRo4HUMIHRMB0GA1UdDgQWBBRdyNm4aGshfm6TZhyvRPWtX/JDhzALBgNVHQ8EBAMCBsAwHwYDVR0jBBgwFoAUMKhsmrIyB0dDRdSLhcQ7DZU+ff8wFgYDVR0lAQH/BAwwCgYIKwYBBQUHAwgwIgYIYIZIAYb4RAIEFgwUU0Y5MTQ0MDMwMDM1OTk2MTA4NkEwJAYKKoEchu8yAgEBAQQWDBRTRjkxNDQwMzAwMzU5OTYxMDg2QTAgBgYqVgsHAQgEFgwUU0Y5MTQ0MDMwMDM1OTk2MTA4NkEwCgYIKoEcz1UBg3UDRwAwRAIgLqBZZwWG2p3R2insocMeewoJVdEkT4LwDDCqb7ujfhYCIASehhySMz53jrvmJhRVyO1sNpRgsR0v/DVIpe2KNC6MMYIB1DCCAdACAQEwgbMwgZgxFjAUBgNVBAMMDUhMSkNBX1NNMl9TVUIxDjAMBgNVBAsMBUhMSkNBMTMwMQYDVQQKDCrpu5HpvpnmsZ/nnIHmlbDlrZfor4HkuaborqTor4HmnInpmZDlhazlj7gxFTATBgNVBAcMDOWTiOWwlOa7qOW4gjEVMBMGA1UECAwM6buR6b6Z5rGf55yBMQswCQYDVQQGEwJDTgIWIoE1NQNFEgFTYTAQV2g5KYBzYnJEFzAMBggqgRzPVQGDEQUAoIGwMBoGCSqGSIb3DQEJAzENBgsqhkiG9w0BCRABBDAcBgkqhkiG9w0BCQUxDxcNMjUwODI4MDg0NTQ1WjAvBgkqhkiG9w0BCQQxIgQgMy2j/zgIrql/ZPSMpI4hPRuIHcH7Vz5V9u44lGH9XcEwQwYLKoZIhvcNAQkQAi8xNDAyMDAwLjAKBggqgRzPVQGDEQQgQ+r5Yf/8GZYPadH7UCbo+2A7fAT4gT1S3zF/7+EDLcMwDAYIKoEcz1UBg3UFAARGMEQCIEM7LjUOCIFVWqHrym524D0AshuL1c3QnQUeVv75TErxAiAuBbjWmUDBfkHvyKgxje9Ooxw/hZiBABvmalMfbwgUug=="}}
-----------------------------------
