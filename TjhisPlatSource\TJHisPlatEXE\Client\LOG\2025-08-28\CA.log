2025-08-28 16:06:51
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:51
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:51
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:06:51
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:51
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:51
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:51
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:51
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:51
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:51
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:06:51
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:51
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:51
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:52
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:52
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:52
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:52
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:52
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:52
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:52
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:52
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:52
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:52
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:53
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:53
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:53
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:53
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:53
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:53
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:53
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:53
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:53
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:53
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:54
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:54
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:54
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:54
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:54
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:54
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:54
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:54
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:54
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:54
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:55
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:55
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:55
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:55
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:55
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:55
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:55
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:55
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:55
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:55
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:56
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:56
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:56
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:56
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:56
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:56
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:56
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:56
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:56
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:56
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:57
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:57
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:57
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:57
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:57
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:57
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:57
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:57
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:57
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:57
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:58
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:58
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:58
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:58
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:58
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:58
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:58
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:58
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:58
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:58
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:59
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:59
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:59
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:59
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:59
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:59
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:06:59
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:06:59
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:59
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:06:59
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:00
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:00
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:00
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:00
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:00
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:00
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:00
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:00
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:00
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:00
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:01
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:01
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:01
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:01
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:01
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:01
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:01
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:01
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:01
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:01
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:02
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:02
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:02
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:02
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:02
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:02
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:02
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:02
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:02
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:02
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:03
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:03
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:03
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:03
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:03
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:03
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:03
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:03
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:03
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:03
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:04
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:04
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:04
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:04
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:04
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:04
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:04
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:04
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:04
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:04
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:05
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:05
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:05
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:05
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:05
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:05
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:05
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:05
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:05
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:05
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:06
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:06
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:06
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"lxb68t2yrukxqtxd"}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"lxb68t2yrukxqtxd","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "lxb68t2yrukxqtxd",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:06
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:06
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:06
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"fvd4uyurzyt6hnai"}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"fvd4uyurzyt6hnai","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "fvd4uyurzyt6hnai",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-28 16:07:06
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-28 16:07:06
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:06
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"6876d30539334bc380de2b92eb06053d_9"}}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=6876d30539334bc380de2b92eb06053d_9;pars:{"transactionId":"f07qvwwsr4kurdr5"}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"f07qvwwsr4kurdr5","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-28 16:07:06
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "f07qvwwsr4kurdr5",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
