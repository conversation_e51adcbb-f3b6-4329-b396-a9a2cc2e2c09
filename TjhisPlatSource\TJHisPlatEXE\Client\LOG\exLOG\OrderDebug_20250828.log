[2025-08-28 17:09:08.764] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 0 -> 4
[2025-08-28 17:09:08.765] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 2） - MaxOrderNo变化: 0 -> 4
[2025-08-28 17:09:24.412] [ORDER_SYNC] [2025-8-22] ORDER_NO同步检查 - 内存最大: 4, 数据库最大: 4, 分配: 5
[2025-08-28 17:09:24.413] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 4 -> 5
[2025-08-28 17:09:24.421] [MAXNO] [OrderBusiness.Add] MaxOrderNo递增（内存中医嘱数量: 2） - MaxOrderNo变化: 4 -> 5
[2025-08-28 17:09:24.422] [ORDER] [OrderBusiness.Add] 新增医嘱 - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 5, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: , ORDER_CLASS: 
[2025-08-28 17:09:29.353] [CHECKPOINT] [UcDrugPresc.Save] 检查点[开始保存] - 西药保存按钮被点击
[2025-08-28 17:09:29.354] [CHECKPOINT] [UcDrugPresc.Save] 待保存医嘱 - 医嘱数量: 0
[2025-08-28 17:09:29.354] [CHECKPOINT] [UcDrugPresc.Save] 检查点[无医嘱保存] - ordersSave为空或数量为0
[2025-08-28 17:09:29.369] [CHECKPOINT] [UcCdrugPresc.Save] 开始保存 - 中药保存按钮被点击
[2025-08-28 17:09:29.369] [BATCH] [UcCdrugPresc.Save] 待保存医嘱 - 医嘱数量: 1
[2025-08-28 17:09:29.369] [BATCH] [UcCdrugPresc.Save] 待保存医嘱[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 5, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 17:09:29.374] [BATCH] [OrderBusiness.Save] 保存前状态 - 医嘱数量: 1
[2025-08-28 17:09:29.374] [BATCH] [OrderBusiness.Save] 保存前状态[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 5, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 17:09:29.374] [CHECKPOINT] [OrderBusiness.Save] 检查点[开始保存] - 医嘱数量: 1, MaxOrderNo: 5
[2025-08-28 17:09:29.786] [CHECKPOINT] [OrderBusiness.Save] 检查点[保存时ITEM_NO] - ORDER_NO: 6, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁
[2025-08-28 17:09:29.807] [CHECKPOINT] [OrderBusiness.Save] 检查点[费用记录处理] - ORDER_TEXT: 砂仁, ORDER_NO: 6, ORDER_SUB_NO: 1, ITEM_NO: 1, STATE: 保存
[2025-08-28 17:09:29.811] [CHECKPOINT] [OrderBusiness.Save] 检查点[数据库执行前] - SQL语句数量: 3
[2025-08-28 17:09:29.852] [CHECKPOINT] [OrderBusiness.Save] 检查点[数据库执行] - 执行结果: True
[2025-08-28 17:09:30.320] [CHECKPOINT] [OrderBusiness.Save] 检查点[保存成功] - 已重置缓存，MaxOrderNo: 5
[2025-08-28 17:09:30.321] [CHECKPOINT] [OrderBusiness.Save] 数据库执行 - 执行结果: True
[2025-08-28 17:09:30.355] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 5 -> 6
[2025-08-28 17:09:30.355] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 3） - MaxOrderNo变化: 5 -> 6
[2025-08-28 17:09:30.921] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 3） - MaxOrderNo变化: 6 -> 6
[2025-08-28 17:09:31.369] [CHECKPOINT] [UcCdrugPresc.Save] 保存成功 - 中药保存成功
[2025-08-28 17:09:31.554] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 3） - MaxOrderNo变化: 6 -> 6
[2025-08-28 17:09:40.031] [ORDER_SYNC] [2025-8-22] ORDER_NO同步检查 - 内存最大: 6, 数据库最大: 6, 分配: 7
[2025-08-28 17:09:40.032] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 6 -> 7
[2025-08-28 17:09:40.046] [MAXNO] [OrderBusiness.Add] MaxOrderNo递增（内存中医嘱数量: 3） - MaxOrderNo变化: 6 -> 7
[2025-08-28 17:09:40.047] [ORDER] [OrderBusiness.Add] 新增医嘱 - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 7, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: , ORDER_CLASS: 
[2025-08-28 17:09:53.810] [CHECKPOINT] [UcDrugPresc.Save] 检查点[开始保存] - 西药保存按钮被点击
[2025-08-28 17:09:53.811] [CHECKPOINT] [UcDrugPresc.Save] 待保存医嘱 - 医嘱数量: 0
[2025-08-28 17:09:53.811] [CHECKPOINT] [UcDrugPresc.Save] 检查点[无医嘱保存] - ordersSave为空或数量为0
[2025-08-28 17:09:53.813] [CHECKPOINT] [UcCdrugPresc.Save] 开始保存 - 中药保存按钮被点击
[2025-08-28 17:09:53.814] [BATCH] [UcCdrugPresc.Save] 待保存医嘱 - 医嘱数量: 1
[2025-08-28 17:09:53.814] [BATCH] [UcCdrugPresc.Save] 待保存医嘱[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 7, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 17:09:53.814] [BATCH] [OrderBusiness.Save] 保存前状态 - 医嘱数量: 1
[2025-08-28 17:09:53.815] [BATCH] [OrderBusiness.Save] 保存前状态[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 7, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 17:09:53.815] [CHECKPOINT] [OrderBusiness.Save] 检查点[开始保存] - 医嘱数量: 1, MaxOrderNo: 7
[2025-08-28 17:09:54.141] [CHECKPOINT] [OrderBusiness.Save] 检查点[保存时ITEM_NO] - ORDER_NO: 8, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁
[2025-08-28 17:09:54.158] [CHECKPOINT] [OrderBusiness.Save] 检查点[费用记录处理] - ORDER_TEXT: 砂仁, ORDER_NO: 8, ORDER_SUB_NO: 1, ITEM_NO: 1, STATE: 保存
[2025-08-28 17:09:54.159] [CHECKPOINT] [OrderBusiness.Save] 检查点[数据库执行前] - SQL语句数量: 3
[2025-08-28 17:09:54.191] [CHECKPOINT] [OrderBusiness.Save] 检查点[数据库执行] - 执行结果: True
[2025-08-28 17:09:54.460] [CHECKPOINT] [OrderBusiness.Save] 检查点[保存成功] - 已重置缓存，MaxOrderNo: 7
[2025-08-28 17:09:54.461] [CHECKPOINT] [OrderBusiness.Save] 数据库执行 - 执行结果: True
[2025-08-28 17:09:54.496] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 7 -> 8
[2025-08-28 17:09:54.497] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 4） - MaxOrderNo变化: 7 -> 8
[2025-08-28 17:09:55.013] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 4） - MaxOrderNo变化: 8 -> 8
[2025-08-28 17:09:55.463] [CHECKPOINT] [UcCdrugPresc.Save] 保存成功 - 中药保存成功
[2025-08-28 17:09:55.527] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 4） - MaxOrderNo变化: 8 -> 8
[2025-08-28 17:10:37.973] [ORDER_SYNC] [2025-8-22] ORDER_NO同步检查 - 内存最大: 8, 数据库最大: 8, 分配: 9
[2025-08-28 17:10:37.973] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 8 -> 9
[2025-08-28 17:10:37.998] [MAXNO] [OrderBusiness.Add] MaxOrderNo递增（内存中医嘱数量: 4） - MaxOrderNo变化: 8 -> 9
[2025-08-28 17:10:37.998] [ORDER] [OrderBusiness.Add] 新增医嘱 - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 9, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: , ORDER_CLASS: 
[2025-08-28 17:10:42.268] [CHECKPOINT] [UcDrugPresc.Save] 检查点[开始保存] - 西药保存按钮被点击
[2025-08-28 17:10:42.269] [CHECKPOINT] [UcDrugPresc.Save] 待保存医嘱 - 医嘱数量: 0
[2025-08-28 17:10:42.269] [CHECKPOINT] [UcDrugPresc.Save] 检查点[无医嘱保存] - ordersSave为空或数量为0
[2025-08-28 17:10:42.272] [CHECKPOINT] [UcCdrugPresc.Save] 开始保存 - 中药保存按钮被点击
[2025-08-28 17:10:42.272] [BATCH] [UcCdrugPresc.Save] 待保存医嘱 - 医嘱数量: 1
[2025-08-28 17:10:42.272] [BATCH] [UcCdrugPresc.Save] 待保存医嘱[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 9, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 17:10:42.273] [BATCH] [OrderBusiness.Save] 保存前状态 - 医嘱数量: 1
[2025-08-28 17:10:42.273] [BATCH] [OrderBusiness.Save] 保存前状态[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 9, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 17:10:42.273] [CHECKPOINT] [OrderBusiness.Save] 检查点[开始保存] - 医嘱数量: 1, MaxOrderNo: 9
[2025-08-28 17:10:42.407] [UcCdrugPresc.Save] 错误: 保存异常: 药品【砂仁】库存不足，只能开0克
预扣库存16克！
[2025-08-28 17:10:47.836] [CHECKPOINT] [UcDrugPresc.Save] 检查点[开始保存] - 西药保存按钮被点击
[2025-08-28 17:10:47.836] [CHECKPOINT] [UcDrugPresc.Save] 待保存医嘱 - 医嘱数量: 0
[2025-08-28 17:10:47.836] [CHECKPOINT] [UcDrugPresc.Save] 检查点[无医嘱保存] - ordersSave为空或数量为0
[2025-08-28 17:10:47.839] [CHECKPOINT] [UcCdrugPresc.Save] 开始保存 - 中药保存按钮被点击
[2025-08-28 17:10:47.839] [BATCH] [UcCdrugPresc.Save] 待保存医嘱 - 医嘱数量: 1
[2025-08-28 17:10:47.839] [BATCH] [UcCdrugPresc.Save] 待保存医嘱[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 9, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 17:10:47.840] [BATCH] [OrderBusiness.Save] 保存前状态 - 医嘱数量: 1
[2025-08-28 17:10:47.840] [BATCH] [OrderBusiness.Save] 保存前状态[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 9, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 17:10:47.840] [CHECKPOINT] [OrderBusiness.Save] 检查点[开始保存] - 医嘱数量: 1, MaxOrderNo: 9
[2025-08-28 17:10:47.909] [UcCdrugPresc.Save] 错误: 保存异常: 药品【砂仁】库存不足，只能开0克
预扣库存16克！
