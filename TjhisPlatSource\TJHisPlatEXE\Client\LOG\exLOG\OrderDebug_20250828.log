[2025-08-28 16:36:11.984] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 0 -> 6
[2025-08-28 16:36:11.985] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 3） - MaxOrderNo变化: 0 -> 6
[2025-08-28 16:36:23.854] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 2） - MaxOrderNo变化: 6 -> 6
[2025-08-28 16:36:24.486] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 2） - MaxOrderNo变化: 6 -> 6
[2025-08-28 16:36:27.324] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 6 -> 2
[2025-08-28 16:36:27.324] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 1） - MaxOrderNo变化: 6 -> 2
[2025-08-28 16:36:27.684] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 1） - MaxOrderNo变化: 2 -> 2
[2025-08-28 16:36:35.976] [ORDER_SYNC] [2025-8-22] ORDER_NO同步检查 - 内存最大: 2, 数据库最大: 2, 分配: 3
[2025-08-28 16:36:35.976] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 2 -> 3
[2025-08-28 16:36:35.992] [MAXNO] [OrderBusiness.Add] MaxOrderNo递增（内存中医嘱数量: 1） - MaxOrderNo变化: 2 -> 3
[2025-08-28 16:36:35.993] [ORDER] [OrderBusiness.Add] 新增医嘱 - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 3, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: , ORDER_CLASS: 
[2025-08-28 16:36:40.249] [CHECKPOINT] [UcDrugPresc.Save] 检查点[开始保存] - 西药保存按钮被点击
[2025-08-28 16:36:40.250] [CHECKPOINT] [UcDrugPresc.Save] 待保存医嘱 - 医嘱数量: 0
[2025-08-28 16:36:40.251] [CHECKPOINT] [UcDrugPresc.Save] 检查点[无医嘱保存] - ordersSave为空或数量为0
[2025-08-28 16:36:40.265] [CHECKPOINT] [UcCdrugPresc.Save] 开始保存 - 中药保存按钮被点击
[2025-08-28 16:36:40.265] [BATCH] [UcCdrugPresc.Save] 待保存医嘱 - 医嘱数量: 1
[2025-08-28 16:36:40.265] [BATCH] [UcCdrugPresc.Save] 待保存医嘱[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 3, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 16:36:40.270] [BATCH] [OrderBusiness.Save] 保存前状态 - 医嘱数量: 1
[2025-08-28 16:36:40.270] [BATCH] [OrderBusiness.Save] 保存前状态[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 3, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 16:36:40.270] [CHECKPOINT] [OrderBusiness.Save] 检查点[开始保存] - 医嘱数量: 1, MaxOrderNo: 3
[2025-08-28 16:36:41.169] [CHECKPOINT] [OrderBusiness.Save] 检查点[保存时ITEM_NO] - ORDER_NO: 4, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁
[2025-08-28 16:36:41.190] [CHECKPOINT] [OrderBusiness.Save] 检查点[费用记录处理] - ORDER_TEXT: 砂仁, ORDER_NO: 4, ORDER_SUB_NO: 1, ITEM_NO: 1, STATE: 保存
[2025-08-28 16:36:41.193] [CHECKPOINT] [OrderBusiness.Save] 检查点[数据库执行前] - SQL语句数量: 3
[2025-08-28 16:36:41.227] [CHECKPOINT] [OrderBusiness.Save] 检查点[数据库执行] - 执行结果: True
[2025-08-28 16:36:41.864] [CHECKPOINT] [OrderBusiness.Save] 检查点[保存成功] - 已重置缓存，MaxOrderNo: 3
[2025-08-28 16:36:41.865] [CHECKPOINT] [OrderBusiness.Save] 数据库执行 - 执行结果: True
[2025-08-28 16:36:41.897] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 3 -> 4
[2025-08-28 16:36:41.898] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 2） - MaxOrderNo变化: 3 -> 4
[2025-08-28 16:36:42.272] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 2） - MaxOrderNo变化: 4 -> 4
[2025-08-28 16:36:42.728] [CHECKPOINT] [UcCdrugPresc.Save] 保存成功 - 中药保存成功
[2025-08-28 16:36:42.914] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 2） - MaxOrderNo变化: 4 -> 4
[2025-08-28 16:36:51.758] [ORDER_SYNC] [2025-8-22] ORDER_NO同步检查 - 内存最大: 4, 数据库最大: 4, 分配: 5
[2025-08-28 16:36:51.758] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 4 -> 5
[2025-08-28 16:36:51.776] [MAXNO] [OrderBusiness.Add] MaxOrderNo递增（内存中医嘱数量: 2） - MaxOrderNo变化: 4 -> 5
[2025-08-28 16:36:51.776] [ORDER] [OrderBusiness.Add] 新增医嘱 - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 5, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: , ORDER_CLASS: 
[2025-08-28 16:36:58.260] [CHECKPOINT] [UcDrugPresc.Save] 检查点[开始保存] - 西药保存按钮被点击
[2025-08-28 16:36:58.261] [CHECKPOINT] [UcDrugPresc.Save] 待保存医嘱 - 医嘱数量: 0
[2025-08-28 16:36:58.261] [CHECKPOINT] [UcDrugPresc.Save] 检查点[无医嘱保存] - ordersSave为空或数量为0
[2025-08-28 16:36:58.264] [CHECKPOINT] [UcCdrugPresc.Save] 开始保存 - 中药保存按钮被点击
[2025-08-28 16:36:58.264] [BATCH] [UcCdrugPresc.Save] 待保存医嘱 - 医嘱数量: 1
[2025-08-28 16:36:58.264] [BATCH] [UcCdrugPresc.Save] 待保存医嘱[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 5, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 16:36:58.264] [BATCH] [OrderBusiness.Save] 保存前状态 - 医嘱数量: 1
[2025-08-28 16:36:58.265] [BATCH] [OrderBusiness.Save] 保存前状态[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 5, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 16:36:58.265] [CHECKPOINT] [OrderBusiness.Save] 检查点[开始保存] - 医嘱数量: 1, MaxOrderNo: 5
[2025-08-28 16:36:58.548] [CHECKPOINT] [OrderBusiness.Save] 检查点[保存时ITEM_NO] - ORDER_NO: 6, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁
[2025-08-28 16:36:58.564] [CHECKPOINT] [OrderBusiness.Save] 检查点[费用记录处理] - ORDER_TEXT: 砂仁, ORDER_NO: 6, ORDER_SUB_NO: 1, ITEM_NO: 1, STATE: 保存
[2025-08-28 16:36:58.566] [CHECKPOINT] [OrderBusiness.Save] 检查点[数据库执行前] - SQL语句数量: 3
[2025-08-28 16:36:58.599] [CHECKPOINT] [OrderBusiness.Save] 检查点[数据库执行] - 执行结果: True
[2025-08-28 16:36:59.410] [CHECKPOINT] [OrderBusiness.Save] 检查点[保存成功] - 已重置缓存，MaxOrderNo: 5
[2025-08-28 16:36:59.410] [CHECKPOINT] [OrderBusiness.Save] 数据库执行 - 执行结果: True
[2025-08-28 16:36:59.452] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 5 -> 6
[2025-08-28 16:36:59.452] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 3） - MaxOrderNo变化: 5 -> 6
[2025-08-28 16:36:59.839] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 3） - MaxOrderNo变化: 6 -> 6
[2025-08-28 16:37:00.286] [CHECKPOINT] [UcCdrugPresc.Save] 保存成功 - 中药保存成功
[2025-08-28 16:37:00.361] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 3） - MaxOrderNo变化: 6 -> 6
[2025-08-28 16:38:22.416] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 0 -> 6
[2025-08-28 16:38:22.416] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 3） - MaxOrderNo变化: 0 -> 6
