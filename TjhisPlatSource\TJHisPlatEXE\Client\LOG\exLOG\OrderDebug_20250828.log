[2025-08-28 17:05:45.980] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 0 -> 6
[2025-08-28 17:05:45.994] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 3） - MaxOrderNo变化: 0 -> 6
[2025-08-28 17:05:51.111] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 6 -> 4
[2025-08-28 17:05:51.111] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 2） - MaxOrderNo变化: 6 -> 4
[2025-08-28 17:05:51.744] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 2） - MaxOrderNo变化: 4 -> 4
[2025-08-28 17:05:54.021] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 4 -> 2
[2025-08-28 17:05:54.033] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 1） - MaxOrderNo变化: 4 -> 2
[2025-08-28 17:05:54.479] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 1） - MaxOrderNo变化: 2 -> 2
[2025-08-28 17:06:02.362] [ORDER_SYNC] [2025-8-22] ORDER_NO同步检查 - 内存最大: 2, 数据库最大: 2, 分配: 3
[2025-08-28 17:06:02.362] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 2 -> 3
[2025-08-28 17:06:02.380] [MAXNO] [OrderBusiness.Add] MaxOrderNo递增（内存中医嘱数量: 1） - MaxOrderNo变化: 2 -> 3
[2025-08-28 17:06:02.380] [ORDER] [OrderBusiness.Add] 新增医嘱 - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 3, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: , ORDER_CLASS: 
[2025-08-28 17:06:11.672] [CHECKPOINT] [UcDrugPresc.Save] 检查点[开始保存] - 西药保存按钮被点击
[2025-08-28 17:06:11.673] [CHECKPOINT] [UcDrugPresc.Save] 待保存医嘱 - 医嘱数量: 0
[2025-08-28 17:06:11.674] [CHECKPOINT] [UcDrugPresc.Save] 检查点[无医嘱保存] - ordersSave为空或数量为0
[2025-08-28 17:06:11.687] [CHECKPOINT] [UcCdrugPresc.Save] 开始保存 - 中药保存按钮被点击
[2025-08-28 17:06:11.688] [BATCH] [UcCdrugPresc.Save] 待保存医嘱 - 医嘱数量: 1
[2025-08-28 17:06:11.688] [BATCH] [UcCdrugPresc.Save] 待保存医嘱[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 3, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 17:06:11.692] [BATCH] [OrderBusiness.Save] 保存前状态 - 医嘱数量: 1
[2025-08-28 17:06:11.692] [BATCH] [OrderBusiness.Save] 保存前状态[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 3, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 17:06:11.692] [CHECKPOINT] [OrderBusiness.Save] 检查点[开始保存] - 医嘱数量: 1, MaxOrderNo: 3
[2025-08-28 17:06:12.236] [CHECKPOINT] [OrderBusiness.Save] 检查点[保存时ITEM_NO] - ORDER_NO: 4, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁
[2025-08-28 17:06:12.257] [CHECKPOINT] [OrderBusiness.Save] 检查点[费用记录处理] - ORDER_TEXT: 砂仁, ORDER_NO: 4, ORDER_SUB_NO: 1, ITEM_NO: 1, STATE: 保存
[2025-08-28 17:06:12.261] [CHECKPOINT] [OrderBusiness.Save] 检查点[数据库执行前] - SQL语句数量: 3
[2025-08-28 17:06:12.295] [CHECKPOINT] [OrderBusiness.Save] 检查点[数据库执行] - 执行结果: True
[2025-08-28 17:06:12.762] [CHECKPOINT] [OrderBusiness.Save] 检查点[保存成功] - 已重置缓存，MaxOrderNo: 3
[2025-08-28 17:06:12.762] [CHECKPOINT] [OrderBusiness.Save] 数据库执行 - 执行结果: True
[2025-08-28 17:06:12.829] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 3 -> 4
[2025-08-28 17:06:12.830] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 2） - MaxOrderNo变化: 3 -> 4
[2025-08-28 17:06:13.957] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 2） - MaxOrderNo变化: 4 -> 4
[2025-08-28 17:06:14.809] [CHECKPOINT] [UcCdrugPresc.Save] 保存成功 - 中药保存成功
[2025-08-28 17:06:14.982] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 2） - MaxOrderNo变化: 4 -> 4
[2025-08-28 17:06:20.573] [ORDER_SYNC] [2025-8-22] ORDER_NO同步检查 - 内存最大: 4, 数据库最大: 4, 分配: 5
[2025-08-28 17:06:20.573] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 4 -> 5
[2025-08-28 17:06:20.589] [MAXNO] [OrderBusiness.Add] MaxOrderNo递增（内存中医嘱数量: 2） - MaxOrderNo变化: 4 -> 5
[2025-08-28 17:06:20.590] [ORDER] [OrderBusiness.Add] 新增医嘱 - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 5, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: , ORDER_CLASS: 
[2025-08-28 17:06:27.786] [CHECKPOINT] [UcDrugPresc.Save] 检查点[开始保存] - 西药保存按钮被点击
[2025-08-28 17:06:27.787] [CHECKPOINT] [UcDrugPresc.Save] 待保存医嘱 - 医嘱数量: 0
[2025-08-28 17:06:27.787] [CHECKPOINT] [UcDrugPresc.Save] 检查点[无医嘱保存] - ordersSave为空或数量为0
[2025-08-28 17:06:27.789] [CHECKPOINT] [UcCdrugPresc.Save] 开始保存 - 中药保存按钮被点击
[2025-08-28 17:06:27.789] [BATCH] [UcCdrugPresc.Save] 待保存医嘱 - 医嘱数量: 1
[2025-08-28 17:06:27.789] [BATCH] [UcCdrugPresc.Save] 待保存医嘱[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 5, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 17:06:27.790] [BATCH] [OrderBusiness.Save] 保存前状态 - 医嘱数量: 1
[2025-08-28 17:06:27.790] [BATCH] [OrderBusiness.Save] 保存前状态[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 5, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 17:06:27.790] [CHECKPOINT] [OrderBusiness.Save] 检查点[开始保存] - 医嘱数量: 1, MaxOrderNo: 5
[2025-08-28 17:06:27.914] [UcCdrugPresc.Save] 错误: 保存异常: 药品【砂仁】库存不足，只能开6克
预扣库存10克！
