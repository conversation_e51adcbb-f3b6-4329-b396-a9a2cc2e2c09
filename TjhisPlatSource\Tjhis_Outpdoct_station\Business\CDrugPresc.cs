﻿using DevExpress.XtraEditors;
using PlatCommon.SysBase;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Data.Common;
using System.Linq;
using System.Reflection;
using Tjhis.Outpdoct.Station.Common;
using Tjhis.Outpdoct.Station.Dal;
using Tjhis.Outpdoct.Station.Interface;
using Tjhis.Outpdoct.Station.Model;

using PlatCommon.Common;
using Tjhis.Interface.Station.Phstock;

namespace Tjhis.Outpdoct.Station.Business
{
    public class CDrugPresc :PrescBusiness, ICPresc
    {
        public static List<InputResult> inputResult = new List<InputResult>();
        private int prescMaxDays = PrescParameter.MaxPrescDays.ToInt(0);

        // 缓存最大ITEM_NO，避免重复查询数据库
        private static decimal? _cachedMaxItemNo = null;

        CdrugProjectItemsDal CdrugProjectItemsDal { get; set; }
        CdrugProjectMasterDal CdrugProjectMasterDal { get; set; }
        VcdrugDictDal cDrugDictDal { get; set; }
        VstaticDecoctionVsChargeDal VstaticDecoctionVsChargeDal { get; set; }
        CDrugUsageDescDal CDrugUsageDescDal { get; set; }

        ClinicItemDictDal ClinicItemDictDal { get; set; }

        private DeptDictDal deptDictDal { get; set; }
        #region 释放
        public override void Dispose(bool disposed)
        {
            if (CdrugProjectItemsDal != null)
                CdrugProjectItemsDal.Dispose();
            if (CdrugProjectMasterDal != null)
                CdrugProjectMasterDal.Dispose();
            DrugDictDal.Dispose();
            if (cDrugDictDal != null)
                cDrugDictDal.Dispose();
            if (VstaticDecoctionVsChargeDal != null)
                VstaticDecoctionVsChargeDal.Dispose();
            if (CDrugUsageDescDal != null)
                CDrugUsageDescDal.Dispose();
            if (deptDictDal != null)
                deptDictDal.Dispose();
            base.Dispose(disposed);
        }
        #endregion

        public CDrugPresc(IOrders OrderBusiness):base(OrderBusiness)
        {
            CdrugProjectItemsDal = new CdrugProjectItemsDal();
            CdrugProjectMasterDal = new CdrugProjectMasterDal();
            DrugDictDal = new DrugDictDal();
            cDrugDictDal = new VcdrugDictDal();
            VstaticDecoctionVsChargeDal = new VstaticDecoctionVsChargeDal();
            CDrugUsageDescDal = new CDrugUsageDescDal();
            deptDictDal = new DeptDictDal();
            ClinicItemDictDal = new ClinicItemDictDal();
        }
        /// <summary>
        /// 草药自动分方处理(只处理新增草药记录) liujun add 2023-3-13
        /// </summary>
        /// <param name="rows"></param>
        /// <returns></returns>
        public bool AutoDividePresc(List<OUTP_ORDERS_STANDARD> rows, List<OUTP_ORDERS_STANDARD> allRows = null)
        {
            // 日志：记录分方前的医嘱信息
            WriteDebugLog($"[CDrugPresc.AutoDividePresc] 分方前状态 - 医嘱数量: {rows?.Count ?? 0}");
            if (rows != null)
            {
                for (int i = 0; i < rows.Count; i++)
                {
                    var order = rows[i];
                    WriteDebugLog($"[CDrugPresc.AutoDividePresc] 分方前状态[{i + 1}] - CLINIC_NO: {order.CLINIC_NO}, ORDER_NO: {order.ORDER_NO}, ORDER_SUB_NO: {order.ORDER_SUB_NO}, ITEM_NO: {order.ITEM_NO}, ORDER_TEXT: {order.ORDER_TEXT}, ORDER_CLASS: {order.ORDER_CLASS}, STATE: {order.STATE}");
                }
            }
            WriteDebugLog($"[CDrugPresc.AutoDividePresc] 检查点[开始分方] - AutoDetachDrug参数: {PrescParameter.AutoDetachDrug}");

            // 添加库存检查 - 在分方前先检查库存
            if (rows != null && rows.Count > 0)
            {
                try
                {
                    WriteDebugLog("[CDrugPresc.AutoDividePresc] 开始执行库存检查");
                    CheckForDataIntegrity(rows);
                    WriteDebugLog("[CDrugPresc.AutoDividePresc] 库存检查通过");
                }
                catch (Exception ex)
                {
                    WriteDebugLog($"[CDrugPresc.AutoDividePresc] 库存检查失败: {ex.Message}");
                    throw; // 重新抛出异常，阻止保存
                }
            }

            base.AutoDivideApply(rows);
            if (PrescParameter.AutoDetachDrug != "1")
            {
                WriteDebugLog("[CDrugPresc.AutoDividePresc] 检查点[跳过分方] - AutoDetachDrug != 1");
                return true;
            }
            if (rows.Count == 0)
            {
                return true;
            }
            if (!rows.All(r => r.ORDER_CLASS.ToString("") == "B"))
            {
                throw new Exception("只处理新增草药记录");
            }

            try
            {

                // 获取当前最大ORDER_NO，用于代煎费等特殊项目
                int maxOrderNo = this.OrderBusiness.getMaxOrderNo() + 1;
                //这里只处理新增的
                List<OUTP_ORDERS_STANDARD> list = rows.Where(row => "新方".Equals(row.APPOINT_NO)).OrderBy(r => r.PERFORMED_BY.ToString()).ThenBy(r => r.DRUG_INDICATOR.ToString()).ThenBy(r => r.ORDER_NO.ToInt()).ToList();
                if(list.Count == 0)//这里没有新增的处方，不进行分单，只对orderNo和orderSubNo进行赋值
                {
                    // 修复：对于非"新方"状态的中药，也需要确保ORDER_NO的唯一性
                    // 这种情况通常发生在中药的APPOINT_NO已经被设置为处方号的情况下
                    decimal maxItemNo = GetMaxItemNoForPatient();

                    // 日志：记录进入非"新方"分支的原因
                    WriteDebugLog($"[CDrugPresc.AutoDividePresc] 检查点[非新方分支] - 进入非新方分支，医嘱数量: {rows?.Count ?? 0}");

                    rows.ForEach(order =>
                    {
                        if (order.STATE.Equals(Constants.NEW_ORDER_STATE_STR))
                        {
                            // 修复：确保ORDER_NO的唯一性，即使不是"新方"状态
                            // 检查当前ORDER_NO是否与其他医嘱冲突
                            var conflictOrders = rows.Where(o => o != order && o.ORDER_NO == order.ORDER_NO && o.ORDER_SUB_NO == order.ORDER_SUB_NO).ToList();
                            if (conflictOrders.Count > 0)
                            {
                                // 发现冲突，重新分配ORDER_NO
                                int newOrderNo = maxOrderNo++;
                                WriteDebugLog($"[CDrugPresc.AutoDividePresc] 检查点[ORDER_NO冲突修复] - 原ORDER_NO: {order.ORDER_NO}, 新ORDER_NO: {newOrderNo}, ORDER_TEXT: {order.ORDER_TEXT}");
                                order.ORDER_NO = newOrderNo;
                                order.ORDER_SUB_NO = 1;
                            }

                            // 只确保ITEM_NO的全局唯一性
                            if (order.ITEM_NO <= 0)
                            {
                                order.ITEM_NO = ++maxItemNo;
                            }
                            int itemNo = 1;
                            order.OrderCosts.ForEach(costs =>
                            {
                                costs.ORDER_NO = order.ORDER_NO;
                                costs.ORDER_SUB_NO = order.ORDER_SUB_NO;
                                costs.ITEM_NO = itemNo++;
                            });
                        }
                    });
                }
                else
                {
                    string dispensary = string.Empty;
                    string drugIndicator = string.Empty;
                    string toxiProperty = string.Empty;

                    int maxPrescCount = PrescParameter.MaxCDrugCount;
                    int curPrescCount = 0;
                    string prescNo = string.Empty;
                    List<string> prescList = new List<string>();
                    foreach (OUTP_ORDERS_STANDARD row in list)
                    {
                        string curDispensary = row.PERFORMED_BY.ToString();
                        string curDrugIndicator = row.DRUG_INDICATOR.ToString();
                        string curToxiProperty = row.TOXI_PROPERTY.ToString();
                        if (dispensary != curDispensary || drugIndicator != curDrugIndicator || (maxPrescCount > 0 && curPrescCount >= maxPrescCount))
                        {
                            curPrescCount = 0;
                        }

                        if (curPrescCount == 0)
                        {
                            prescNo = DalBaseFunc.GetSequeceValue("处方号序列", PlatCommon.SysBase.SystemParm.HisUnitCode);
                        }

                        curPrescCount++;

                        row.APPOINT_NO = prescNo;
                        dispensary = curDispensary;
                        drugIndicator = curDrugIndicator;
                        toxiProperty = curToxiProperty;
                        // 修复：不重新分配ORDER_NO和ORDER_SUB_NO，保持之前的分配
                        // row.ORDER_NO = maxOrderNo++;
                        // row.ORDER_SUB_NO = 1;
                        if (!prescList.Contains(prescNo))
                        {
                            prescList.Add(prescNo);
                        }
                    }

                    foreach (string no in prescList)
                    {
                        List<OUTP_ORDERS_STANDARD> prescs = list.Where(r => r.APPOINT_NO == no).ToList();

                        // 修复：为同一个处方的中药分配正确的ORDER_NO和ORDER_SUB_NO
                        if (prescs.Count > 0)
                        {
                            // 为同一个处方的所有中药分配相同的ORDER_NO
                            int prescOrderNo = maxOrderNo++;
                            WriteDebugLog($"[CDrugPresc.AutoDividePresc] 检查点[处方分组] - 处方号: {no}, 分配ORDER_NO: {prescOrderNo}, 中药数量: {prescs.Count}");

                            // 为每个中药分配递增的ORDER_SUB_NO
                            for (int i = 0; i < prescs.Count; i++)
                            {
                                var presc = prescs[i];
                                presc.ORDER_NO = prescOrderNo;
                                presc.ORDER_SUB_NO = i + 1;  // ORDER_SUB_NO从1开始
                                WriteDebugLog($"[CDrugPresc.AutoDividePresc] 检查点[ORDER_SUB_NO分配] - ORDER_TEXT: {presc.ORDER_TEXT}, ORDER_NO: {presc.ORDER_NO}, ORDER_SUB_NO: {presc.ORDER_SUB_NO}");
                            }
                        }

                        bool isPoison = false;
                        if (prescs.Any(r => r.PRESC_ATTR.Contains("毒") || r.PRESC_ATTR.Contains("麻")))
                        {
                            isPoison = true;
                        }
                        //如果列表中的代煎项目代码不为空，则生成代煎费
                        bool isNeedCreateDecoction = prescs.Count(c => !string.IsNullOrEmpty(c.DECOCTION_ITEM_CODE)) > 0;
                        if (isNeedCreateDecoction)
                        {
                            // 修复：为代煎费分配新的ORDER_NO，确保不与现有医嘱冲突
                            int decoctionOrderNo = this.OrderBusiness.getMaxOrderNo() + 1;
                            // 注意：无法直接设置MaxOrderNo，因为它不在IOrders接口中
                            // 代煎费的ORDER_NO会在CreateDecotion方法中处理
                            this.CreateDecotion(rows, no, prescs.FirstOrDefault(), decoctionOrderNo);
                        }
                        prescs.ForEach(r => r.PRESC_ATTR = isPoison ? "毒麻处方" : string.IsNullOrEmpty(r.PRESC_ATTR)?"普通处方":r.PRESC_ATTR);

                        // 修复：正确处理费用记录的ITEM_NO分配，每个医嘱内部从1开始
                        foreach (var presc in prescs)
                        {
                            if (presc.OrderCosts != null && presc.OrderCosts.Count > 0)
                            {
                                WriteDebugLog($"[CDrugPresc.AutoDividePresc] 检查点[费用记录处理] - ORDER_TEXT: {presc.ORDER_TEXT}, ORDER_NO: {presc.ORDER_NO}, ORDER_SUB_NO: {presc.ORDER_SUB_NO}, 费用记录数量: {presc.OrderCosts.Count}");
                                int costItemNo = 1; // 每个医嘱的费用记录ITEM_NO从1开始
                                foreach (var costs in presc.OrderCosts)
                                {
                                    // 记录修改前的状态
                                    WriteDebugLog($"[CDrugPresc.AutoDividePresc] 检查点[费用记录修改前] - ORDER_TEXT: {presc.ORDER_TEXT}, 原ORDER_NO: {costs.ORDER_NO}, 原ORDER_SUB_NO: {costs.ORDER_SUB_NO}, 原ITEM_NO: {costs.ITEM_NO}");

                                    costs.ORDER_NO = presc.ORDER_NO;
                                    costs.ORDER_SUB_NO = presc.ORDER_SUB_NO;
                                    costs.ITEM_NO = costItemNo++;

                                    // 修复：确保费用记录的其他关键字段也正确设置
                                    costs.CLINIC_NO = presc.CLINIC_NO;
                                    costs.PATIENT_ID = presc.PATIENT_ID;
                                    costs.VISIT_DATE = presc.VISIT_DATE;
                                    costs.VISIT_NO = presc.VISIT_NO;
                                    costs.SERIAL_NO = presc.SERIAL_NO;

                                    WriteDebugLog($"[CDrugPresc.AutoDividePresc] 检查点[费用ITEM_NO分配] - ORDER_TEXT: {presc.ORDER_TEXT}, 新ORDER_NO: {costs.ORDER_NO}, 新ORDER_SUB_NO: {costs.ORDER_SUB_NO}, 新费用ITEM_NO: {costs.ITEM_NO}, CLINIC_NO: {costs.CLINIC_NO}");
                                }
                            }
                            else
                            {
                                WriteDebugLog($"[CDrugPresc.AutoDividePresc] 检查点[无费用记录] - ORDER_TEXT: {presc.ORDER_TEXT}, ORDER_NO: {presc.ORDER_NO}, ORDER_SUB_NO: {presc.ORDER_SUB_NO}");
                            }
                        }
                    }
                }
                

            }
            catch (Exception ex)
            {
                // 日志：记录分方异常
                WriteDebugLog($"[CDrugPresc.AutoDividePresc] 错误: 分方异常: {ex.Message}");
                throw ex;
            }

            // 日志：记录分方后的医嘱信息
            WriteDebugLog($"[CDrugPresc.AutoDividePresc] 分方后状态 - 医嘱数量: {rows?.Count ?? 0}");
            if (rows != null)
            {
                for (int i = 0; i < rows.Count; i++)
                {
                    var order = rows[i];
                    WriteDebugLog($"[CDrugPresc.AutoDividePresc] 分方后状态[{i + 1}] - CLINIC_NO: {order.CLINIC_NO}, ORDER_NO: {order.ORDER_NO}, ORDER_SUB_NO: {order.ORDER_SUB_NO}, ITEM_NO: {order.ITEM_NO}, ORDER_TEXT: {order.ORDER_TEXT}, ORDER_CLASS: {order.ORDER_CLASS}, STATE: {order.STATE}");
                }
            }
            WriteDebugLog("[CDrugPresc.AutoDividePresc] 检查点[分方完成] - 成功");

            return true;
        }

        private void CreateDecotion(List<OUTP_ORDERS_STANDARD> rows, string no, OUTP_ORDERS_STANDARD oUTP_ORDERS_STANDARD, int maxOrderNo)
        {
            if(null == oUTP_ORDERS_STANDARD)
            {
                return;
            }
            OUTP_ORDERS_STANDARD orderDecotion = rows.FirstOrDefault(f => f.ORDER_CLASS.Equals("Z") && f.APPOINT_NO.Equals(no) && oUTP_ORDERS_STANDARD.DECOCTION_ITEM_CODE.Equals(f.DECOCTION_ITEM_CODE));
            bool isDecotionClinicItemExist = orderDecotion != null;
            if (!isDecotionClinicItemExist)
            {
                CLINIC_ITEM_DICT clinicItem = this.ClinicItemDictDal.GetSingle(new CLINIC_ITEM_DICT {ITEM_CODE= oUTP_ORDERS_STANDARD.DECOCTION_ITEM_CODE,ITEM_CLASS="Z"});
                if(null == clinicItem)
                {
                    throw new MessageException("未查询到代煎的诊疗项目");
                }
                InputResult decotionResult = new InputResult();
                decotionResult.ItemName = clinicItem.ITEM_NAME;
                decotionResult.ItemCode = clinicItem.ITEM_CODE;
                decotionResult.ItemClass = clinicItem.ITEM_CLASS;
                decotionResult.ItemSpec = "";

                // 保持原有逻辑，但修复ORDER_SUB_NO和ITEM_NO冲突问题
                orderDecotion = this.OrderBusiness.New();
                orderDecotion.ORDER_NO = maxOrderNo;

                // 修复：确保ORDER_SUB_NO不与现有中药冲突
                // 煎药机煎药的ORDER_SUB_NO应该是所有中药ORDER_SUB_NO的最大值+1
                decimal maxOrderSubNo = 0;
                if (rows != null && rows.Count > 0)
                {
                    maxOrderSubNo = rows.Where(r => r.ORDER_NO == maxOrderNo).Max(r => r.ORDER_SUB_NO);
                }
                orderDecotion.ORDER_SUB_NO = maxOrderSubNo + 1;

                orderDecotion.SERIAL_NO = rows[0].SERIAL_NO;
                orderDecotion.OUTP_SERIAL_NO = rows[0].OUTP_SERIAL_NO;
                orderDecotion.OrderCosts= new List<OUTP_ORDERS_COSTS_STANDARD>();

                // 修复：确保ITEM_NO不为0，使用安全的ITEM_NO分配方法
                if (orderDecotion.ITEM_NO <= 0)
                {
                    // 获取当前患者的最大ITEM_NO，确保唯一性
                    decimal maxItemNo = 0;
                    if (rows != null && rows.Count > 0)
                    {
                        maxItemNo = rows.Max(r => r.ITEM_NO);
                    }
                    orderDecotion.ITEM_NO = maxItemNo + 1;
                }

                // 日志：记录煎药机煎药医嘱创建
                WriteDebugLog($"[CDrugPresc.CreateDecotion] 检查点[煎药机煎药医嘱创建] - ORDER_TEXT: {decotionResult.ItemName}, ORDER_NO: {orderDecotion.ORDER_NO}, ORDER_SUB_NO: {orderDecotion.ORDER_SUB_NO}, ITEM_NO: {orderDecotion.ITEM_NO}");

                int itemNo = 1;
                if (orderDecotion.OrderCosts != null)
                {
                    orderDecotion.OrderCosts.ForEach(costs =>
                    {
                        costs.ORDER_NO = orderDecotion.ORDER_NO;
                        costs.ORDER_SUB_NO = orderDecotion.ORDER_SUB_NO;
                        costs.SERIAL_NO = orderDecotion.SERIAL_NO;
                        costs.OUTP_SERIAL_NO = orderDecotion.OUTP_SERIAL_NO;
                        costs.ITEM_NO = itemNo++;

                        // 日志：记录煎药机煎药费用记录
                        WriteDebugLog($"[CDrugPresc.CreateDecotion] 检查点[煎药机煎药费用记录] - ORDER_NO: {costs.ORDER_NO}, ORDER_SUB_NO: {costs.ORDER_SUB_NO}, ITEM_NO: {costs.ITEM_NO}");
                    });
                }
                string strMsg = "";
                if(!this.OrderBusiness.SetClinciItem(orderDecotion, rows, decotionResult,ref strMsg))
                {
                    if (!string.IsNullOrEmpty(strMsg))
                    {
                        throw new MessageException(strMsg);
                    }
                }

                if (orderDecotion.OrderCosts != null)
                {
                    orderDecotion.OrderCosts.ForEach(costs =>
                    {
                        orderDecotion.PRICE = costs.PRICE;
                        orderDecotion.CHARGE_PRICE = costs.CHARGE_PRICE;
                        costs.AMOUNT = oUTP_ORDERS_STANDARD.REPETITION;
                        costs.COSTS = decimal.Round(costs.PRICE * costs.AMOUNT.ToDecimal(1), "AB".Contains(costs.ORDER_CLASS) ? 4 : 2);
                        costs.CHARGES = decimal.Round(costs.CHARGE_PRICE * costs.AMOUNT.ToDecimal(1), "AB".Contains(costs.ORDER_CLASS) ? 4 : 2);
                    });
                }

                orderDecotion.AMOUNT = oUTP_ORDERS_STANDARD.REPETITION;
                orderDecotion.COSTS= decimal.Round(orderDecotion.PRICE * orderDecotion.AMOUNT.ToDecimal(1), "AB".Contains(orderDecotion.ORDER_CLASS) ? 4 : 2);
                orderDecotion.CHARGES = decimal.Round(orderDecotion.CHARGE_PRICE * orderDecotion.AMOUNT.ToDecimal(1), "AB".Contains(orderDecotion.ORDER_CLASS) ? 4 : 2);

                rows.Add(orderDecotion);
            }
        }

        public void SetSubPresc(List<OUTP_ORDERS_STANDARD> dts, OUTP_ORDERS_STANDARD currentRow)
        {
            throw new Exception("草药不能设置子处方");
        }
        
        /// <summary>
        /// 提示或验证指定行用药天数是否符合医保限制 liujun add 2023-3-15
        /// </summary>
        /// <param name="handle"></param>
        /// <param name="chargeType"></param>
        /// <param name="abidance"></param>
        /// <param name="amount"></param>
        /// <param name="insurAbidance"></param>
        /// <returns></returns>
        private bool CheckInsurAbidance(OUTP_ORDERS_STANDARD row)
        {
            //string drugFlag;
            //string clinicType = string.Empty;

            //// 是否医保患者
            //if (CommonMethod.StringIsNull(this.PatientInfo.INSURANCE_FLAG) || !this.PatientInfo.INSURANCE_FLAG.Equals("1") || this.PatientInfo.ISREFORM_FLAG.Equals("3"))
            //    return 100;

            //// 是否医保药品
            //if (this.PatientInfo.ISREFORM_FLAG.Equals("0") && this.PatientInfo.INSURANCE_FLAG.Equals("1"))
            //{
            //    drugFlag = this.gvPresc.GetRowCellValue(handle, "INSURANCE_FLAG").ToString("");
            //    if (CommonMethod.StringIsNull(drugFlag) || drugFlag.Equals("0"))
            //        return 100;

            //    if (abidance > 30 && !amount.Equals(1))
            //    {
            //        UIMessageBox.Warn("医保患者开药天数不能大于30天，请调整医保处方用药天数后再进行保存！");
            //        return -1;
            //    }

            //    if (insurAbidance > 300)
            //    {
            //        UIMessageBox.Warn("医保患者开药不能超过300天用量，请调整药品数量、或频次用法后再进行保存！");
            //        return -1;
            //    }
            //}


            //// 医保患者验证用药天数
            //if (this.PatientInfo.ISREFORM_FLAG.Equals("1"))
            //{
            //    switch (this.PatientInfo.EMERGFLAG)
            //    {
            //        case "1":
            //            if (abidance > 3 && !amount.Equals(1))
            //            {
            //                UIMessageBox.Warn("急诊军人病人开药天数不能大于3天！");
            //                return -1;
            //            }
            //            break;
            //        case "3":
            //            // 行动不便
            //            if (abidance > 14 && !amount.Equals(1))
            //            {
            //                UIMessageBox.Warn("普通军人大病开药天数不能大于14天！");
            //                return -1;
            //            }
            //            break;
            //        case "4":
            //            // 十种大病、慢病
            //            if (abidance > this.PatientInfo.MAX_MEDICATION_DAYS && !amount.Equals(1))
            //            {
            //                UIMessageBox.Warn("普通军人\"" + this.PatientInfo.CHRONIC_DISEASE_DESC + "\"开药天数不能大于" + this.PatientInfo.MAX_MEDICATION_DAYS + "天！");
            //                return -1;
            //            }
            //            break;
            //        default:
            //            if (abidance > 7 && !amount.Equals(1))
            //            {
            //                UIMessageBox.Warn("普通军人病人开药天数已经大于7天，请核实病人情况！");
            //                return -1;
            //            }
            //            break;
            //    }
            //}
            //else if (this.PatientInfo.ISREFORM_FLAG.Equals("2"))
            //{
            //    switch (this.PatientInfo.EMERGFLAG)
            //    {
            //        case "1":
            //            if (abidance > 3 && !amount.Equals(1))
            //            {
            //                UIMessageBox.Warn("急诊军人病人开药天数不能大于3天！");
            //                return -1;
            //            }
            //            break;
            //        default:
            //            if (abidance > 7 && !amount.Equals(1))
            //            {
            //                UIMessageBox.Warn("普通军人病人开药天数已经大于7天，请核实病人情况！");
            //                return -1;
            //            }
            //            break;
            //    }
            //}

            return true;
        }
        /// <summary>
        /// 处方加药处理，现在处方的规是只有新增没有修改（保存过的） liujun add 2023-2-24
        /// </summary>
        /// <param name="result"></param>
        /// <param name="row"></param>
        /// <param name="dts"></param>
        /// <returns></returns>
        public bool AddDrug(OutpPatientInfo pi, InputResult result, OUTP_ORDERS_STANDARD order,List<OUTP_ORDERS_STANDARD> orderList,ref string strMsg)
        {
            inputResult.Add(result);
            bool bResult = true;
            try
            {
                if (order == null)
                {
                    strMsg = "没有获取到当前数据行！";
                    bResult= false;
                }

                if (order.PERFORMED_BY != result.Performed_dept)
                {
                    strMsg = string.Format("所选药品【{0}】药局与当前药品药局不一致，无法直接替换，\r\n请切到相同药局，或删除当前药品以后再重新录入！", result.ItemName);
                    bResult = false;

                }
                bResult = AddDrugBase(pi, result, order, orderList,ref strMsg);
            }
            catch (Exception ex)
            {

                throw ex;
            }

            return bResult;
        }
        /// <summary>
        /// 数量发生变化时的数据处理 liujun add 2023-3-14
        /// </summary>
        /// <param name="row"></param>
        /// <param name="dts"></param>
        /// <returns></returns>
        private bool AmountChanged(OUTP_ORDERS_STANDARD orders, List<OUTP_ORDERS_STANDARD> ordersList)
        {
            if (orders == null)
            {
                throw new MessageException("要处理的数据行为空！");
            }

            if (orders.ORDER_CLASS.ToString("") != "B")
            {
                throw new MessageException("只能处理草药处方！");
            }

            decimal amount = orders.AMOUNT.ToDecimal(0);
            string drugCode = orders.ORDER_CODE.ToString("");
            string drugName = orders.ORDER_TEXT.ToString("");
            string drugSpec = orders.ITEM_SPEC.ToString("");
            string firmId = orders.FIRM_ID.ToString("");
            string units = orders.UNITS.ToString("");
            decimal price = orders.PRICE.ToDecimal(-1);
            decimal chargePrice = orders.CHARGE_PRICE.ToDecimal(-1);
            InputResult result = new InputResult();
            result.ItemClass = "B";
            result.ItemCode = drugCode;
            result.ItemSpec = drugSpec + firmId;
            result.ItemUnit = units;

            if (price == -1 || chargePrice == -1)
            {
                if (!priceListDal.GetClinicItemPrice(result, this.OrderBusiness.GetCurrentPatient().CHARGE_TYPE, ref price, ref chargePrice))
                {
                    throw new MessageException("获取【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的药品的价格信息出错!");
                }
            }

            orders.COSTS = Math.Round(amount * price, PrescParameter.CostsPrecision, MidpointRounding.AwayFromZero);
            orders.CHARGES = Math.Round(amount * chargePrice, PrescParameter.ChargesPrecision, MidpointRounding.AwayFromZero);
            orders.ITEM_PRICE = chargePrice;
            orders.CHARGE_PRICE = chargePrice;
            orders.PRICE = price;
            int orderNo = orders.ORDER_NO.ToInt(-1);
            int orderSubNo = orders.ORDER_SUB_NO.ToInt(-1);
            #region 处理与医嘱相关的费用记录
            orders.OrderCosts.Where(r => r.ORDER_CLASS.ToString("") == "B" && r.ORDER_NO.ToInt() == orderNo && r.ORDER_SUB_NO.ToInt() == orderSubNo && r.ITEM_NO.ToString() == "1").ToList().ForEach(r =>
            {
                r.COSTS = Math.Round(amount * price, PrescParameter.CostsPrecision, MidpointRounding.AwayFromZero);
                r.CHARGES = Math.Round(amount * chargePrice, PrescParameter.ChargesPrecision, MidpointRounding.AwayFromZero);
                r.AMOUNT = Math.Round(amount);
                r.ITEM_PRICE = chargePrice;
            });

            #endregion 处理与医嘱相关的费用记录

            string frequency = orders.FREQUENCY.ToString("");
            if (CommFunc.StrEmpty(frequency))
            {
                return true;
            }

            decimal dosage = this.CalculateSplitDosage(orders);
            decimal doscPerUnit = orders.DOSE_PER_UNIT.ToDecimal(0);
            decimal amountPerPackage = orders.AMOUNT_PER_PACKAGE.ToDecimal(0);

            orders.DOSAGE = dosage;

            int freqCounter = orders.FREQ_COUNTER.ToInt(0);
            int freqInterval = orders.FREQ_INTERVAL.ToInt(0);
            string freqIntervalUnits = orders.FREQ_INTERVAL_UNIT.ToString("");
            decimal performTimes = 0;
            decimal abidance = 0;
            decimal insurAbidance = 0;
            string splitFlag = string.Empty;

            #region 处理用药天数，与执行次数
            this.CalculateInsurAbidance(amount, amountPerPackage, doscPerUnit, dosage, freqIntervalUnits, freqInterval, freqCounter, splitFlag, ref performTimes, ref abidance, ref insurAbidance);

            orders.ABIDANCE = insurAbidance;

            List<OUTP_ORDERS_STANDARD> list = ordersList.Where(r => r.ORDER_CLASS.ToString("") == "B").OrderBy(r => r.ORDER_NO.ToInt()).ThenBy(r => r.ORDER_SUB_NO.ToInt()).ToList();
            int handle = ordersList.FindIndex(r => r == orders);
            if (handle > 0)
            {
                OUTP_ORDERS_STANDARD upRow = list[handle - 1];
                orders.ABIDANCE = upRow.ABIDANCE;
                orders.PERFORM_TIMES = upRow.PERFORM_TIMES;
            }

            // 若当前执行次数为0，则动态设置成1
            if (performTimes.Equals(0))
            {
                performTimes = 1;
            }

            orders.ABIDANCE = abidance;
            orders.PERFORM_TIMES = performTimes;
            #endregion 处理用药天数，与执行次数

            // 修复：使用当前医嘱的ORDER_SUB_NO，而不是硬编码的1
            orders.OrderCosts.Where(r => r.ORDER_CLASS.ToString("") == "B" && r.ORDER_NO.ToInt() == orderNo && r.ORDER_SUB_NO.ToInt() == orderSubNo).ToList().ForEach(r =>
            {
                decimal costAmount = performTimes;
                r.AMOUNT = costAmount;
                r.COSTS = Math.Round(r.PRICE.ToDecimal(0) * costAmount, PrescParameter.CostsPrecision, MidpointRounding.AwayFromZero);
                r.CHARGES = Math.Round(r.CHARGE_PRICE.ToDecimal(0) * costAmount, PrescParameter.ChargesPrecision, MidpointRounding.AwayFromZero);

                // 日志：记录费用记录更新
                WriteDebugLog($"[CDrugPresc.CalculateAmount] 检查点[费用记录更新] - ORDER_TEXT: {orders.ORDER_TEXT}, ORDER_NO: {r.ORDER_NO}, ORDER_SUB_NO: {r.ORDER_SUB_NO}, ITEM_NO: {r.ITEM_NO}, AMOUNT: {r.AMOUNT}");
            });
            return true;
        }
        /// <summary>
        /// 途径发生改变 liujun add 2023-3-14
        /// </summary>
        /// <param name="row"></param>
        /// <param name="dts"></param>
        /// <returns></returns>
        private bool AdministrationChanged(OUTP_ORDERS_STANDARD orders, List<OUTP_ORDERS_STANDARD> ordersList)
        {
            if (orders == null)
            {
                throw new MessageException("要处理的数据行为空！");
            }

            if (orders.ORDER_CLASS.ToString("") != "B")
            {
                throw new MessageException("只能处理草药处方！");
            }

            string administration = orders.ADMINISTRATION.ToString("");
            //if (CommFunc.StrEmpty(administration))
            //{
            //    throw new MessageException("途径不能为空");
            //}

            string drugCode = orders.ORDER_CODE.ToString("");
            if (CommFunc.StrEmpty(drugCode))
            {
                throw new MessageException("请先输入药品再录入途径！");
            }


            string drugSpec = orders.ITEM_SPEC.ToString("");

            DataTable table = DrugDictDal.GetAdminstrationForDrug(drugCode, drugSpec, administration);
            decimal normalDosage = 0;
            if (table.Rows.Count != 0)
            {
                DataRow[] ads = table.AsEnumerable().Where(r => !r["DRUG_CODE"].Equals("__")).ToArray();
                if (ads != null && ads.Length > 0)
                {
                    normalDosage = ads[0]["NORMAL_DOSAGE"].ToDecimal(0);
                    orders.DOSAGE = ads[0]["NORMAL_DOSAGE"].ToDecimal(0); //单次用量
                    orders.ADMINISTRATION = ads[0]["ADMINISTRATION_NAME"].ToString(""); //途径
                    orders.FREQUENCY = ads[0]["FREQUENCY"].ToString(); //频率次数
                    orders.FREQ_INTERVAL = ads[0]["FREQ_INTERVAL"].ToString(); //频率间隔
                    orders.FREQ_INTERVAL_UNIT = ads[0]["FREQ_INTERVAL_UNIT"].ToString(); //频率间隔单位
                    orders.FREQ_COUNTER = ads[0]["FREQ_COUNTER"].ToString(); //频率次数

                    string memos = ads[0]["DOCTOR_MEMOS"].ToString("");
                    if (!CommFunc.StrEmpty(memos))
                    {
                        XtraMessageBox.Show("用药提示：" + memos);
                    }
                }
            }

            int orderNo = orders.ORDER_NO.ToInt(-1);
            int orderSubNo = orders.ORDER_SUB_NO.ToInt(-1);
            if (PrescParameter.AutoAdministration == "1")
            {
                IOrderCosts costs = new OrderCostsBusiness();
                costs.AddOutpCostsByOrder(OrderApply.PatientInfo, orders, true, true);
            }

            this.DosageChanged(orders, ordersList);

            return true;
        }
        /// <summary>
        /// 频次发生改变 liujun add 2023-3-14
        /// </summary>
        /// <param name="row"></param>
        /// <param name="dts"></param>
        /// <returns></returns>
        private bool FrequencyChanged(OUTP_ORDERS_STANDARD orders, List<OUTP_ORDERS_STANDARD> ordersList)
        {
            if (orders == null)
            {
                throw new MessageException("要处理的数据行为空！");
            }

            if (orders.ORDER_CLASS.ToString("") != "B")
            {
                throw new MessageException("只能处理草药处方！");
            }

            string frequency = orders.FREQUENCY.ToString("");

            #region 提取频次信息并赋值
            int freqCounter = 0;
            int freqInterval = 0;
            string freqIntervalUnits = "";
            DataTable table = DrugDictDal.GetPerformFreqDictItem(frequency);
            if (!table.Rows.Count.Equals(0))
            {
                freqCounter = table.Rows[0]["FREQ_COUNTER"].ToInt(0);
                freqInterval = table.Rows[0]["FREQ_INTERVAL"].ToInt(0);
                freqIntervalUnits = table.Rows[0]["FREQ_INTERVAL_UNITS"].ToString("");
            }
            #endregion 提取频次信息并赋值

            #region 同步整个草药处方的频次信息
            ordersList.Where(r => r.ORDER_CLASS.ToString("") == "B").ToList().ForEach(r =>
            {
                r.FREQUENCY = frequency;
                int rerepetition = r.REPETITION.ToInt(0);
                r.ABIDANCE = rerepetition;
            });
            #endregion 同步整个草药处方的频次信息

            return true;
        }
        /// <summary>
        /// 单次用量发生改变 liujun add 2023-3-14
        /// </summary>
        /// <param name="row"></param>
        /// <param name="dts"></param>
        /// <returns></returns>
        private bool DosageChanged(OUTP_ORDERS_STANDARD orders, List<OUTP_ORDERS_STANDARD> ordersList)
        {
            if (orders == null)
            {
                throw new MessageException("要处理的数据行为空！");
            }

            if (orders.ORDER_CLASS.ToString("") != "B")
            {
                throw new MessageException("只能处理草药处方！");
            }
                        
            decimal dosage = orders.DOSAGE.ToDecimal(0);
            if (dosage <= 0)
            {
                return false;
            }

            if (this.CheckDosageForDrug(orders, 0, dosage, "", 0, 0).Equals(-1))
            {
                return false;
            }

            // 转换药品拆包消耗量作为单次实际使用量
            dosage = this.CalculateSplitDosage(orders);
            orders.DOSAGE = dosage;

            string drugCode = orders.ORDER_CODE.ToString("");
            string drugName = orders.ORDER_TEXT.ToString("");
            string drugSpec = orders.ITEM_SPEC.ToString("");
            string firmId = orders.FIRM_ID.ToString("");
            string units = orders.UNITS.ToString("");
            int repetition = orders.REPETITION.ToInt(0);
            decimal dosPerUnit = orders.DOSE_PER_UNIT.ToDecimal(0);
            decimal amountPerPackage = orders.AMOUNT_PER_PACKAGE.ToDecimal(0);
            decimal amount = dosage * repetition / (dosPerUnit * amountPerPackage);
            decimal price = orders.PRICE.ToDecimal(-1);
            decimal chargePrice = orders.CHARGE_PRICE.ToDecimal(-1);
            InputResult result = new InputResult();
            result.ItemClass = "B";
            result.ItemCode = drugCode;
            result.ItemName = drugName;
            result.ItemSpec = drugSpec + firmId;
            result.Performed_dept = orders.PERFORMED_BY.ToString("");

            if (price.Equals(-1) || chargePrice.Equals(-1))
            {
                if (!priceListDal.GetClinicItemPrice(result, OrderApply.PatientInfo.CHARGE_TYPE, ref price, ref chargePrice))
                {
                    throw new MessageException("获取【编码：" + drugCode + "】，【名称：" + drugName + "】的药品的价格信息出错!");
                }
            }

            orders.AMOUNT = Math.Round(amount, 4);
            orders.COSTS = Math.Round(amount * price,PrescParameter.CostsPrecision, MidpointRounding.AwayFromZero);
            orders.CHARGES = Math.Round(amount * chargePrice,PrescParameter.ChargesPrecision, MidpointRounding.AwayFromZero);
            if (orders.OrderCosts != null && orders.OrderCosts.Count > 0)
            {
                orders.OrderCosts[0].AMOUNT = Math.Round(amount, 4);
                orders.OrderCosts[0].COSTS = Math.Round(amount * price, PrescParameter.CostsPrecision, MidpointRounding.AwayFromZero);
                orders.OrderCosts[0].CHARGES = Math.Round(amount * chargePrice, PrescParameter.ChargesPrecision, MidpointRounding.AwayFromZero);
            }

            return true;
        }
        /// <summary>
        /// 付数发生变化 liujun add 2023-3-15
        /// </summary>
        /// <param name="row"></param>
        /// <param name="dts"></param>
        /// <returns></returns>
        private bool RepetitionChanged(OUTP_ORDERS_STANDARD prescListOrder, List<OUTP_ORDERS_STANDARD> ordersList)
        {
            if (prescListOrder == null)
            {
                throw new MessageException("要处理的数据行为空！");
            }

            if (prescListOrder.ORDER_CLASS.ToString("") != "B")
            {
                throw new MessageException("只能处理草药处方！");
            }

            // 添加调试日志
            WriteDebugLog($"[CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: {prescListOrder.APPOINT_NO}, 剂数: {prescListOrder.REPETITION}");

            string frequency = prescListOrder.FREQUENCY.ToString("");
            int abidance = 0;
            int repetition = prescListOrder.REPETITION.ToInt(0);
            abidance = repetition;

            // 修复：只处理当前处方下的草药，避免影响其他处方
            List<OUTP_ORDERS_STANDARD> currentPrescDrugs = ordersList
                .Where(r => r.ORDER_CLASS.ToString("") == "B" &&
                           r.APPOINT_NO.ToString("").Equals(prescListOrder.APPOINT_NO.ToString("")))
                .ToList();

            WriteDebugLog($"[CDrugPresc.RepetitionChanged] 找到当前处方药品数量: {currentPrescDrugs.Count}");

            foreach (OUTP_ORDERS_STANDARD item in currentPrescDrugs)
            {
                WriteDebugLog($"[CDrugPresc.RepetitionChanged] 处理药品: {item.ORDER_TEXT}, 处方号: {item.APPOINT_NO}");
                SetAmout(prescListOrder, item);
            }

            WriteDebugLog($"[CDrugPresc.RepetitionChanged] 剂数变化处理完成");
            return true;
        }
        /// <summary>
        /// 用药天数改变 liujun add 2023-3-15
        /// </summary>
        /// <param name="row"></param>
        /// <param name="dts"></param>
        /// <returns></returns>
        private bool AbidanceChanged(OUTP_ORDERS_STANDARD orders, List<OUTP_ORDERS_STANDARD> ordersList)
        {
            if (orders == null)
            {
                throw new MessageException("要处理的数据行为空！");
            }

            if (orders.ORDER_CLASS.ToString("") != "B")
            {
                throw new MessageException("只能处理草药处方！");
            }

            decimal abidance = orders.ABIDANCE.ToInt(0);
            if (abidance < 0)
            {
                abidance = 1;
                orders.ABIDANCE = 1;
            }

            string freqDesc = orders.FREQUENCY.ToString("");

            if (CommFunc.StrEmpty(freqDesc))
            {
                return false;
            }
            ordersList.Where(r => r.ORDER_CLASS.ToString("") == "B" && !CommFunc.StrEmpty(r.ORDER_TEXT)).ToList().ForEach(r =>
            {
                r.ABIDANCE = abidance;
            });

            //原来有验证用药天数是否符合医保细则的代码
            return true;
        }
        /// <summary>
        /// 当数据行的特定列值发生改变时，数据同步 liujun add 2023-3-6
        /// </summary>
        /// <param name="colName">值发生变化的列</param>
        /// <param name="row">数据变化的数据行</param>
        /// <param name="dts">需要同步的datatables</param>
        /// <returns></returns>
        public bool CellValueChanged(string colName, OUTP_ORDERS_STANDARD orders, List<OUTP_ORDERS_STANDARD> ordersList)
        {

            if (orders == null)
            {
                throw new MessageException("请指定要处理的数据行！");
            }

            if (orders.ORDER_CLASS.ToString("") != "B")
            {
                return false;
            }

            switch (colName)
            {
                case "AMOUNT":
                    return this.AmountChanged(orders, ordersList);
                case "ADMINISTRATION":
                    return this.AdministrationChanged(orders, ordersList);
                case "FREQUENCY":
                    this.FrequencyChanged(orders, ordersList);
                    break;
                case "REPETITION":
                    this.RepetitionChanged(orders, ordersList);
                    break;
                case "DOSAGE":
                    this.DosageChanged(orders, ordersList);
                    break;
                case "ABIDANCE":
                    this.AbidanceChanged(orders, ordersList);
                    break;
                default:
                    break;
            }

            return true;
        }
        /// <summary>
        /// 数据复制
        /// </summary>
        /// <param name="source"></param>
        /// <param name="target"></param>
        private void CopyData(OUTP_ORDERS_STANDARD source,OUTP_ORDERS_STANDARD target)
        {
            if (source == null || target == null)
            {
                return;
            }

            Type type = source.GetType();
            PropertyInfo[] infos = type.GetProperties();
            foreach (var info in infos)
            {
                if (info.Name== "OrderCosts")
                {
                    continue;
                }

                info.SetValue(target, info.GetValue(source, null),null);
            }
        }

        /// <summary>
        /// 处方复制 liujun add 2023-3-7
        /// </summary>
        /// <param name="prescNo"></param>
        /// <param name="presc"></param>
        /// <param name="dts"></param>
        /// <returns></returns>
        public bool CopyPresc(string prescNo,List<OUTP_ORDERS_STANDARD> orders, List<OUTP_ORDERS_STANDARD> hisOrders)
        {
            //if (hisOrders == null || hisOrders.Count == 0)
            //{
            //    throw new MessageException("请选择要复制的处方！");
            //}

            //if (hisOrders.Where(r => r.ORDER_CLASS.ToString() != "B").Count() > 0)
            //{
            //    throw new MessageException("只能复制的草药处方！");
            //}

            ////if (this.addOrder == null)
            ////{
            ////    throw new MessageException("没有增加医嘱的方法！");
            ////}

            //string prescNoIns = string.Empty;

            //var prescGroup = hisOrders.GroupBy(r => new { PrescNo = r.APPOINT_NO.ToString(), OrderNo = r.ORDER_NO.ToInt() }).Select(item => new { item.Key.PrescNo, item.Key.OrderNo, Cnt = item.Count() });
            //foreach (var item in prescGroup)
            //{
            //    List<OUTP_ORDERS_STANDARD> list = hisOrders.Where(r => r.APPOINT_NO.ToString() == item.PrescNo && r.ORDER_NO.ToInt() == item.OrderNo).OrderBy(r => r.ORDER_SUB_NO.ToInt()).ToList();
            //    bool isSub = list.Count > 1 && list[0].ORDER_SUB_NO.ToInt() == 1 ? true : false;
            //    bool isCopy = false;
            //    int copyOrderNo = -1;
            //    int prescCount = 1;
            //    for (int index = 0; index < list.Count; index++)
            //    {
            //        OUTP_ORDERS_STANDARD row = list[index];
            //        prescNoIns = item.PrescNo + item.OrderNo.ToString().PadLeft(4, '0') + row.ORDER_SUB_NO.ToString("").PadLeft(4, '0');

            //        #region 重复用药处理
            //        InputResult result = new InputResult();
            //        result.ItemClass = "B";
            //        result.ItemCode = row.ORDER_CODE.ToString("");
            //        result.ItemName = row.ORDER_TEXT.ToString("");
            //        result.ItemSpec = row.ITEM_SPEC.ToString("") + row.FIRM_ID.ToString("");
            //        result.ItemUnit = row.DOSAGE_UNITS.ToString("");
            //        result.ItemQuantity = row.DOSAGE.ToString();
            //        result.Performed_dept = row.PERFORMED_BY.ToString("");
            //        result.Performed_name = DalDrug.GetDeptName(result.Performed_dept);
            //        result.BatchNo = row.BATCH_NO;
            //        if (orders.Where(r => r.ORDER_CLASS.ToString("") == "B" && r.ORDER_CODE.ToString("") == result.ItemCode).Count() > 0)
            //        {
            //            XtraMessageBox.Show("获取【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的药品已经开过,不允许重复开药!");
            //            continue;
            //        }
            //        #endregion 重复用药处理


            //        OUTP_ORDERS_STANDARD newR = this.addOrder.Invoke();
            //        if (isSub && index == 0)
            //        {
            //            isCopy = true;
            //            copyOrderNo = newR.ORDER_NO.ToInt();
            //        }
            //        else
            //        {
            //            if (isCopy)
            //            {
            //                prescCount++;
            //                newR.ORDER_NO = copyOrderNo;
            //                int maxSubNo = orders.Where(r => r.ORDER_CLASS.ToString("") == "B" && r.ORDER_NO.ToInt() == copyOrderNo).Max(r => r.ORDER_SUB_NO.ToInt());
            //                newR.ORDER_SUB_NO = maxSubNo + 1;
            //            }
            //        }

            //        #region 使用历史处方记录复制成为新的医嘱
            //        int orderNo = newR.ORDER_NO.ToInt();
            //        int orderSubNo = newR.ORDER_SUB_NO.ToInt();

            //        CopyData(row, newR);
            //        newR.ORDER_NO = orderNo;
            //        newR.ORDER_SUB_NO = orderSubNo;
            //        newR.APPOINT_NO = prescNo;
            //        #endregion 使用历史处方记录复制成为新的医嘱

            //        #region 提取药品信息（药品基础信息、价格、皮试、高危等），同时判断是否重复开药
            //        DrugPriceListItem priceListItem = DalPriceList.GetDrugPriceListItem(result);
            //        string drugSpec = priceListItem.DRUG_SPEC ?? string.Empty;
            //        string firmId = priceListItem.FIRM_ID ?? string.Empty;
            //        string units = priceListItem.UNITS ?? string.Empty;
            //        decimal amountPerPackage = priceListItem.AMOUNT_PER_PACKAGE;
            //        string minSpec = priceListItem.MIN_SPEC ?? string.Empty;
            //        decimal price = 0;
            //        decimal chargePrice = 0;
            //        string armyResult = this.MilitaryMedicalReform(result.ItemClass, result.ItemCode, result.ItemName, drugSpec);
            //        if (armyResult == "")
            //        {
            //            return false;
            //        }

            //        if (!DalPriceList.GetClinicItemPrice(result, this.PatientInfo.CHARGE_TYPE, ref price, ref chargePrice))
            //        {
            //            XtraMessageBox.Show("获取【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的药品的价格信息出错!");
            //            continue;
            //        }

            //        decimal amount = row.AMOUNT.ToDecimal(0);
            //        DRUG_INFO drugInfo = DalDrug.GetDrugInfoItem(result.ItemCode);
            //        DRUG_DICT drugDict = DalDrug.GetDrugDictItem(result.ItemCode, drugSpec);

            //        string skinTest = drugInfo.SKINTEST ?? string.Empty;
            //        decimal dosage = drugDict.DOSE_PER_UNIT ?? result.ItemQuantity.ToDecimal(0);
            //        string highDanger = CommFunc.StrEmpty(drugDict.HIGH_DANGER) ? string.Empty : drugDict.HIGH_DANGER == "1" ? "▲" : string.Empty;
            //        if (orders.Where(r => r.ORDER_CLASS.ToString("") == "B" && r.ORDER_CODE.ToString("") == result.ItemCode).Count() > 0)
            //        {
            //            XtraMessageBox.Show("获取【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的药品已经开过,不允许重复开药!");
            //            continue;
            //        }
            //        #endregion 提取药品信息（药品基础信息、价格、皮试、高危等），同时判断是否重复开药

            //        #region 药品库存判断
            //        decimal quantity = DalDrug.GetDrugInventoryInStorage(result.ItemCode, drugSpec, firmId, result.Performed_dept);
            //        if (quantity == -999)
            //        {
            //            XtraMessageBox.Show("获取【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的药品在药房【" + result.Performed_name + "】不可供!");
            //            continue;
            //        }

            //        if (quantity <= 0 && PrescParameter.CheckAmount == "1")
            //        {
            //            if (PrescParameter.AmountMustEnough == "1")
            //            {
            //                XtraMessageBox.Show("获取【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的药品在药房【" + result.Performed_name + "】库存不足!");
            //                continue;
            //            }
            //            else
            //            {
            //                XtraMessageBox.Show("获取【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的药品在药房【" + result.Performed_name + "】库存不足!");
            //                continue;
            //            }
            //        }
            //        #endregion 药品库存判断

            //        #region 取药品的拆分属性与公费用药类别
            //        string splitFlag = DalDrug.GetDrugSplitProperty(result.ItemCode, result.Performed_dept);
            //        DataTable table = DalDrug.GetOfficialCatalogInfo(this.PatientInfo.CHARGE_TYPE, result.ItemCode, minSpec);
            //        string officeClass;
            //        string memo;
            //        if (table == null || table.Rows.Count == 0)
            //        {
            //            officeClass = string.Empty;
            //            memo = string.Empty;
            //        }
            //        else
            //        {
            //            officeClass = table.Rows[0]["CLASS_NAME"].ToString(string.Empty);
            //            memo = table.Rows[0]["MEMO"].ToString(string.Empty);
            //        }
            //        #endregion 取药品的拆分属性与公费用药类别

            //        #region 药品的毒理属性
            //        CommFunc.SetDtToNull(table);
            //        string drugToxiProperty = CommFunc.StrEmpty(drugDict.TOXI_PROPERTY) ? "普通药品" : drugDict.TOXI_PROPERTY;
            //        table = DalDrug.GetToxiPropertyInfo(drugToxiProperty);
            //        int drugIndicator = drugDict.DRUG_INDICATOR;
            //        string prescAttrName;
            //        int limitClass;
            //        if (table == null && table.Rows.Count == 0)
            //        {
            //            prescAttrName = "普通处方";
            //            limitClass = 0;
            //        }
            //        else
            //        {
            //            prescAttrName = table.Rows[0]["PRESC_ATTR_NAME"].ToString("普通处方");
            //            limitClass = table.Rows[0]["LIMIT_CLASS"].ToInt(0);
            //        }

            //        if (PrescParameter.AutoDetachDrug == "1" && drugIndicator == 3)
            //        {
            //            drugIndicator = 1;
            //        }

            //        int toxiPorperty = CommFunc.JudgePoisonHempProperties(";" + drugToxiProperty + ";");
            //        if (this.AuditAuthorityForDrug(drugDict))
            //        {
            //            return false;
            //        }
            //        if (PrescParameter.AutoDetachDrug == "0")//不自动分方的情况，只能添加毒理属性相同的药品
            //        {
            //            List<OUTP_ORDERS_STANDARD> findList = orders.Where(r => r.ORDER_CLASS.ToString() == "B").OrderBy(r => r.ORDER_NO.ToInt()).ThenBy(r => r.ORDER_SUB_NO.ToInt()).ToList();

            //            if (findList.Count > 1)
            //            {
            //                int find = findList.FindIndex(r => r == row);
            //                if (find == 0)
            //                {
            //                    find++;
            //                }
            //                else
            //                {
            //                    find--;
            //                }

            //                DRUG_DICT drug = DalDrug.GetDrugDictItem(list[find].ORDER_CODE.ToString(), list[find].ITEM_SPEC.ToString());
            //                int nextToxiProperty = CommFunc.JudgePoisonHempProperties(";" + (CommFunc.StrEmpty(drug.TOXI_PROPERTY) ? "普通药品" : drug.TOXI_PROPERTY) + ";");
            //                if (toxiPorperty != nextToxiProperty)
            //                {
            //                    if (XtraMessageBox.Show("录入药品与已有药品毒理不同，是否在同一处方中录入?", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) == DialogResult.No)
            //                    {
            //                        continue;
            //                    }
            //                }
            //            }
            //        }
            //        #endregion 药品的毒理属性

            //        #region 药品皮试处理
            //        if (skinTest == "1")
            //        {
            //            if (orders.Where(r => r.ORDER_CLASS.ToString() == "B" && r.ORDER_CODE.ToString("") == result.ItemCode && r.SKIN_FLAG.ToString("") == "1").Count() == 0)
            //            {
            //                if (XtraMessageBox.Show("【" + result.ItemName + "】是皮试药品是否进行皮试?", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) == DialogResult.Yes)
            //                {
            //                    skinTest = "1";
            //                }
            //                else
            //                {
            //                    skinTest = "0";
            //                }
            //            }
            //            else
            //            {
            //                skinTest = "0";
            //            }
            //            row.SKIN_FLAG = skinTest.ToInt(0);
            //            if (skinTest == "1")
            //            {
            //                row.ADMINISTRATION = PrescParameter.SkinAdministration;
            //            }
            //        }
            //        #endregion 药品皮试处理

            //        #region 设置药品的记录信息
            //        newR.SPLIT_FLAG = splitFlag.ToInt(0);
            //        newR.OFFICIAL_CATALOG = officeClass;
            //        newR.TOXI_PROPERTY = drugToxiProperty;
            //        newR.DRUG_INDICATOR = drugIndicator;
            //        newR.DOSE_PER_UNIT = drugDict.DOSE_PER_UNIT;
            //        newR.AMOUNT_PER_PACKAGE = amountPerPackage;
            //        newR.MIN_SPEC = minSpec;
            //        newR.PERFORMED_BY = result.Performed_dept;
            //        newR.USABLE_QUANTITY = quantity;
            //        newR.REPETITION = 1;
            //        newR.RECIPETYPE = armyResult;//军队医改2022
            //        newR.PERFORMED_BY_NAME = result.Performed_name;
            //        newR.PRESC_ATTR = prescAttrName;

            //        if (amount == 0)
            //        {
            //            amount = 1;
            //        }

            //        newR.PRICE = Math.Round(amount * price, 4);
            //        newR.CHARGE_PRICE = Math.Round(amount * price, 4);
            //        newR.COSTS = Math.Round(amount * price, PrescParameter.CostsPrecision);
            //        newR.CHARGES = Math.Round(amount * chargePrice, PrescParameter.ChargesPrecision);
            //        newR.HIGH_DANGER = highDanger;
            //        if (highDanger == "▲")
            //        {
            //            XtraMessageBox.Show("【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的药品是高危药品，请注意！", "提示");
            //        }

            //        #endregion 设置药品的记录信息

            //        #region 生成处方记录对应的费用明细
            //        IOrderCosts costs = new OrderCostsBusiness();
            //        if (!costs.AddOutpCostsByOrder(this.PatientInfo, row, false, true))
            //        {
            //            continue;
            //        }
            //        orders.Add(newR);
            //        #endregion 生成处方记录对应的费用明细
            //    }
            //}

            return true;
        }
        /// <summary>
        /// 模板开药 liujun add 2023-3-9
        /// </summary>
        /// <param name="prescNo"></param>
        /// <param name="presc"></param>
        /// <param name="dts"></param>
        /// <returns></returns>
        public bool TemplateCreatePresc(string prescNo, List<OUTP_ORDERS_STANDARD> presc, List<OUTP_ORDERS_STANDARD> templateList)
        {
            //if (templateList == null || templateList.Count == 0)
            //{
            //    throw new MessageException("请选择模板！");
            //}

            //if (templateList.Where(r => r.ORDER_CLASS.ToString() != "B").Count() > 0)
            //{
            //    throw new MessageException("只能选择草药模板！");
            //}

            //if (this.addOrder == null)
            //{
            //    throw new MessageException("没有增加医嘱的方法！");
            //}


            //var projGroup = templateList.GroupBy(r => new { TreatProjectId = r.APPOINT_NO.ToString(), OrderNo = r.ORDER_NO.ToInt() }).Select(item => new { item.Key.TreatProjectId, item.Key.OrderNo, Cnt = item.Count() });
            //foreach (var item in projGroup)
            //{
            //    List<OUTP_ORDERS_STANDARD> list = templateList.Where(r => r.APPOINT_NO.ToString() == item.TreatProjectId && r.ORDER_NO.ToInt() == item.OrderNo).OrderBy(r => r.ORDER_SUB_NO.ToInt()).ToList();
            //    bool isSub = list.Count > 1 && list[0].ORDER_SUB_NO.ToInt() == 1 ? true : false;
            //    bool isCopy = false;
            //    int copyOrderNo = -1;
            //    int prescCount = 1;
            //    for (int index = 0; index < list.Count; index++)
            //    {
            //        OUTP_ORDERS_STANDARD row = list[index];

            //        #region 重复用药处理
            //        InputResult result = new InputResult();
            //        result.ItemClass = "B";
            //        result.ItemCode = row.ORDER_CODE.ToString("");
            //        result.ItemName = row.ORDER_CLASS.ToString("");
            //        result.ItemSpec = row.ITEM_SPEC.ToString("") + row.FIRM_ID.ToString("");
            //        result.ItemUnit = row.UNITS.ToString("");
            //        result.ItemQuantity = row.DOSAGE.ToString();
            //        result.Performed_dept = row.PERFORMED_BY.ToString("");
            //        result.Performed_name = DalDrug.GetDeptName(result.Performed_dept);
            //        result.BatchNo = row.BATCH_NO.ToString("");
            //        if (presc.Where(r => r.ORDER_CLASS.ToString("") == "B" && r.ORDER_CODE.ToString("") == result.ItemCode).Count() > 0)
            //        {
            //            XtraMessageBox.Show("获取【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的药品已经开过,不允许重复开药!");
            //            continue;
            //        }
            //        #endregion 重复用药处理

            //        OUTP_ORDERS_STANDARD newR = this.addOrder.Invoke();
            //        if (isSub && index == 0)
            //        {
            //            isCopy = true;
            //            copyOrderNo = newR.ORDER_NO.ToInt();
            //        }
            //        else
            //        {
            //            if (isCopy)
            //            {
            //                prescCount++;
            //                newR.ORDER_NO = copyOrderNo;
            //                int maxSubNo = templateList.Where(r => r.ORDER_CLASS.ToString("") == "B" && r.ORDER_NO.ToInt() == copyOrderNo).Max(r => r.ORDER_SUB_NO.ToInt());
            //                newR.ORDER_SUB_NO = maxSubNo + 1;
            //            }
            //        }

            //        #region 使用模板记录生成新医嘱
            //        int orderNo = newR.ORDER_NO.ToInt();
            //        int orderSubNo = newR.ORDER_SUB_NO.ToInt();

            //        CopyData(row, newR);
            //        newR.ORDER_NO = orderNo;
            //        newR.ORDER_SUB_NO = orderSubNo;
            //        newR.APPOINT_NO = prescNo;
            //        #endregion 使用模板记录生成新医嘱

            //        #region 提取药品信息（药品基础信息、价格、皮试、高危等），同时判断是否重复开药
            //        DrugPriceListItem priceListItem = DalPriceList.GetDrugPriceListItem(result);
            //        string drugSpec = priceListItem.DRUG_SPEC ?? string.Empty;
            //        string firmId = priceListItem.FIRM_ID ?? string.Empty;
            //        string units = priceListItem.UNITS ?? string.Empty;
            //        decimal amountPerPackage = priceListItem.AMOUNT_PER_PACKAGE;
            //        string minSpec = priceListItem.MIN_SPEC ?? string.Empty;
            //        decimal price = 0;
            //        decimal chargePrice = 0;
            //        string armyResult = this.MilitaryMedicalReform(result.ItemClass, result.ItemCode, result.ItemName, drugSpec);
            //        if (armyResult == "")
            //        {
            //            return false;
            //        }

            //        if (!DalPriceList.GetClinicItemPrice(result, this.PatientInfo.CHARGE_TYPE, ref price, ref chargePrice))
            //        {
            //            XtraMessageBox.Show("获取【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的药品的价格信息出错!");
            //            continue;
            //        }

            //        decimal amount = row.AMOUNT.ToDecimal(0);
            //        DRUG_INFO drugInfo = DalDrug.GetDrugInfoItem(result.ItemCode);
            //        DRUG_DICT drugDict = DalDrug.GetDrugDictItem(result.ItemCode, drugSpec);

            //        string skinTest = drugInfo.SKINTEST ?? string.Empty;
            //        decimal dosage = drugDict.DOSE_PER_UNIT ?? result.ItemQuantity.ToDecimal(0);
            //        string highDanger = CommFunc.StrEmpty(drugDict.HIGH_DANGER) ? string.Empty : drugDict.HIGH_DANGER == "1" ? "▲" : string.Empty;
            //        if (presc.Where(r => r.ORDER_CLASS.ToString("") == "B" && r.ORDER_CODE.ToString("") == result.ItemCode).Count() > 0)
            //        {
            //            XtraMessageBox.Show("获取【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的药品已经开过,不允许重复开药!");
            //            continue;
            //        }
            //        #endregion 提取药品信息（药品基础信息、价格、皮试、高危等），同时判断是否重复开药

            //        #region 药品库存判断
            //        decimal quantity = DalDrug.GetDrugInventoryInStorage(result.ItemCode, drugSpec, firmId, result.Performed_dept);
            //        if (quantity == -999)
            //        {
            //            XtraMessageBox.Show("获取【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的药品在药房【" + result.Performed_name + "】不可供!");
            //            continue;
            //        }

            //        if (quantity <= 0 && PrescParameter.CheckAmount == "1")
            //        {
            //            if (PrescParameter.AmountMustEnough == "1")
            //            {
            //                XtraMessageBox.Show("获取【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的药品在药房【" + result.Performed_name + "】库存不足!");
            //                continue;
            //            }
            //            else
            //            {
            //                XtraMessageBox.Show("获取【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的药品在药房【" + result.Performed_name + "】库存不足!");
            //                continue;
            //            }
            //        }
            //        #endregion 药品库存判断

            //        #region 取药品的拆分属性与公费用药类别
            //        string splitFlag = DalDrug.GetDrugSplitProperty(result.ItemCode, result.Performed_dept);
            //        DataTable table = DalDrug.GetOfficialCatalogInfo(this.PatientInfo.CHARGE_TYPE, result.ItemCode, minSpec);
            //        string officeClass;
            //        string memo;
            //        if (table == null || table.Rows.Count == 0)
            //        {
            //            officeClass = string.Empty;
            //            memo = string.Empty;
            //        }
            //        else
            //        {
            //            officeClass = table.Rows[0]["CLASS_NAME"].ToString(string.Empty);
            //            memo = table.Rows[0]["MEMO"].ToString(string.Empty);
            //        }
            //        #endregion 取药品的拆分属性与公费用药类别

            //        #region 药品的毒理属性
            //        CommFunc.SetDtToNull(table);
            //        string drugToxiProperty = CommFunc.StrEmpty(drugDict.TOXI_PROPERTY) ? "普通药品" : drugDict.TOXI_PROPERTY;
            //        table = DalDrug.GetToxiPropertyInfo(drugToxiProperty);
            //        int drugIndicator = drugDict.DRUG_INDICATOR;
            //        string prescAttrName;
            //        int limitClass;
            //        if (table == null && table.Rows.Count == 0)
            //        {
            //            prescAttrName = "普通处方";
            //            limitClass = 0;
            //        }
            //        else
            //        {
            //            prescAttrName = table.Rows[0]["PRESC_ATTR_NAME"].ToString("普通处方");
            //            limitClass = table.Rows[0]["LIMIT_CLASS"].ToInt(0);
            //        }

            //        if (PrescParameter.AutoDetachDrug == "1" && drugIndicator == 3)
            //        {
            //            drugIndicator = 1;
            //        }

            //        int toxiPorperty = CommFunc.JudgePoisonHempProperties(";" + drugToxiProperty + ";");
            //        if (this.AuditAuthorityForDrug(drugDict))
            //        {
            //            return false;
            //        }
            //        if (PrescParameter.AutoDetachDrug == "0")//不自动分方的情况，只能添加毒理属性相同的药品
            //        {
            //            List<OUTP_ORDERS_STANDARD> findList = presc.Where(r => r.ORDER_CLASS.ToString() == "B").OrderBy(r => r.ORDER_NO.ToInt()).ThenBy(r => r.ORDER_SUB_NO.ToInt()).ToList();

            //            if (findList.Count > 1)
            //            {
            //                int find = findList.FindIndex(r => r == row);
            //                if (find == 0)
            //                {
            //                    find++;
            //                }
            //                else
            //                {
            //                    find--;
            //                }

            //                DRUG_DICT drug = DalDrug.GetDrugDictItem(list[find].ORDER_CODE.ToString(), list[find].ITEM_SPEC.ToString());
            //                int nextToxiProperty = CommFunc.JudgePoisonHempProperties(";" + (CommFunc.StrEmpty(drug.TOXI_PROPERTY) ? "普通药品" : drug.TOXI_PROPERTY) + ";");
            //                if (toxiPorperty != nextToxiProperty)
            //                {
            //                    if (XtraMessageBox.Show("录入药品与已有药品毒理不同，是否在同一处方中录入?", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) == DialogResult.No)
            //                    {
            //                        continue;
            //                    }
            //                }
            //            }
            //        }
            //        #endregion 药品的毒理属性

            //        #region 药品皮试处理
            //        if (skinTest == "1")
            //        {
            //            if (presc.Where(r => r.ORDER_CLASS.ToString() == "B" && r.ORDER_CODE.ToString("") == result.ItemCode && r.SKIN_FLAG.ToString("") == "1").Count() == 0)
            //            {
            //                if (XtraMessageBox.Show("【" + result.ItemName + "】是皮试药品是否进行皮试?", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) == DialogResult.Yes)
            //                {
            //                    skinTest = "1";
            //                }
            //                else
            //                {
            //                    skinTest = "0";
            //                }
            //            }
            //            else
            //            {
            //                skinTest = "0";
            //            }
            //            newR.SKIN_FLAG = skinTest.ToInt(0);
            //            if (skinTest == "1")
            //            {
            //                newR.ADMINISTRATION = PrescParameter.SkinAdministration;
            //            }
            //        }
            //        #endregion 药品皮试处理

            //        #region 设置药品的记录信息
            //        newR.SPLIT_FLAG = splitFlag.ToInt(0);
            //        newR.OFFICIAL_CATALOG = officeClass;
            //        newR.TOXI_PROPERTY = drugToxiProperty;
            //        newR.DRUG_INDICATOR = drugIndicator;
            //        newR.DOSE_PER_UNIT = drugDict.DOSE_PER_UNIT;
            //        newR.AMOUNT_PER_PACKAGE = amountPerPackage;
            //        newR.MIN_SPEC = minSpec;
            //        newR.PERFORMED_BY = result.Performed_dept;
            //        newR.USABLE_QUANTITY = quantity;
            //        newR.RECIPETYPE = armyResult;//军队医改2022
            //        newR.PERFORMED_BY_NAME = result.Performed_name;
            //        newR.PRESC_ATTR = prescAttrName;

            //        if (amount == 0)
            //        {
            //            amount = 1;
            //        }

            //        newR.PRICE = Math.Round(amount * price, 4);
            //        newR.CHARGE_PRICE = Math.Round(amount * price, 4);
            //        newR.COSTS = Math.Round(amount * price, PrescParameter.CostsPrecision);
            //        newR.CHARGES = Math.Round(amount * chargePrice, PrescParameter.ChargesPrecision);
            //        newR.HIGH_DANGER = highDanger;
            //        if (highDanger == "▲")
            //        {
            //            XtraMessageBox.Show("【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的药品是高危药品，请注意！", "提示");
            //        }

            //        #endregion 设置药品的记录信息

            //        #region 生成处方记录对应的费用明细
            //        IOrderCosts costs = new OrderCostsBusiness();
            //        if (!costs.AddOutpCostsByOrder(this.PatientInfo, row, false, true))
            //        {
            //            continue;
            //        }
            //        presc.Add(newR);
            //        #endregion 生成处方记录对应的费用明细
            //    }
            //}

            return true;
        }
        /// <summary>
        /// 保存处方前的数据完整性校验
        /// </summary>
        /// <returns></returns>
        public bool CheckForDataIntegrity(List<OUTP_ORDERS_STANDARD> dts)
        {
            // 处方数据校验
            foreach (OUTP_ORDERS_STANDARD checkRow in dts)
            {
                foreach (OUTP_ORDERS_COSTS_STANDARD cost in checkRow.OrderCosts)
                {
                    if (!(CommFunc.StrEmpty(cost.ORDER_CLASS.ToString("")) || CommFunc.StrEmpty(cost.ITEM_NAME.ToString("")) || cost.AMOUNT.ToDecimal(0).Equals(0)))
                        continue;

                    throw new MessageException("收费项目" + cost.ITEM_NAME.ToString("") + "类别、名称、数量不能为空或0，请调整后再进行保存！");
                }
                string drugName = checkRow.ORDER_TEXT.ToString("");
                if (CommFunc.StrEmpty(checkRow.FREQUENCY.ToString("")))
                {
                    throw new MessageException("药品【" + drugName + "】的频次不能为空，请调整！");
                }

                if (checkRow.PERFORM_TIMES.ToDecimal(0) > 999)
                {
                    throw new MessageException("药品【" + drugName + "】的执行次数【" + checkRow.PERFORM_TIMES.ToDecimal(0) + "】过大，请调整！");
                }

                decimal dosageSpec = this.GetSpecNum(checkRow.ITEM_SPEC.ToString(""));
                if (dosageSpec <= 0)
                {
                    throw new MessageException("药品【" + drugName + "】的规格不合法！");
                }
                if (checkRow.DOSAGE % dosageSpec != 0)
                {
                    throw new MessageException("药品【" + drugName + "】的单次剂量不是规格的倍数，不合法！");
                }

                if (!checkRow.ORDER_NO.ToDecimal(-999).Equals(-999) && checkRow.ABIDANCE.ToDecimal(0).Equals(0))
                {
                    throw new MessageException("药品【" + drugName + "】的用药天数不能为空！");
                }

                if (checkRow.DOSAGE.ToDecimal(0) <= 0)
                {
                    throw new MessageException("药品【" + drugName + "】的单次剂量无效！");
                }

                // 验证单次用量是否超标
                if (checkRow.DOSE_PER_UNIT.ToDecimal(0) > 0 && checkRow.AMOUNT_PER_PACKAGE.ToDecimal(0) > 0)
                {
                    // 单次剂量是否大于总药品剂量
                    if (checkRow.DOSAGE.ToDecimal(0) > checkRow.AMOUNT.ToDecimal(0) * checkRow.DOSE_PER_UNIT.ToDecimal(0) * checkRow.AMOUNT_PER_PACKAGE.ToDecimal(0))
                    {
                        throw new MessageException("所开药品【" + drugName + "】的单次用量不能大于药品总剂量，请调整！");
                    }
                }

                //====================================================================
                //所开药品为1盒时，不判断药品天数是否超出范围
                //====================================================================
                if (this.prescMaxDays > 0 && checkRow.ABIDANCE.ToInt(0) > this.prescMaxDays && checkRow.AMOUNT.ToDecimal(1) != 1)
                {
                    throw new MessageException("药品【" + drugName + "】的药品：【" + drugName + "】用药天数大于处方允许天数：" + this.prescMaxDays + "，请调整后保存");
                }

                // 校验药品是否有库存是否充足
                string drugCode = checkRow.ORDER_CODE.ToString("");
                string drugSpec = checkRow.ITEM_SPEC.ToString("");
                string firmId = checkRow.FIRM_ID.ToString("");
                string dispensary = checkRow.PERFORMED_BY.ToString("");
                decimal quantity = DrugDictDal.GetDrugInventoryInStorage(drugCode, drugSpec, firmId, dispensary);

                if (quantity <= 0)
                {
                    throw new MessageException("药品【" + drugName + "】的药局中药品【" + drugName + "】不可供，请确认！");
                }

                decimal quantity1 = PrescParameter.CheckModel == "2" ? DrugDictDal.GetDrugInventoryInNotBilling(drugCode, drugSpec, firmId, dispensary) : 0;
                decimal quantity2 = PrescParameter.CheckModel == "1" || PrescParameter.CheckModel == "2" ? DrugDictDal.GetDrugInventoryInDispensing(drugCode, drugSpec, firmId, dispensary) : 0;


                if (quantity - quantity1 - quantity2 < checkRow.AMOUNT.ToDecimal(0))
                {
                    throw new MessageException(string.Format("药品【{0}】库存不足！只能开{1:F2}{3}\n预扣库存{2:F2}{3}",
                        drugName,
                        (quantity - quantity1 - quantity2).ToString(),
                         (quantity1 + quantity2).ToString(),
                        checkRow.UNITS)
                        );
                }

                //6.6.2有对药品用药天数的的校验，6.6.4没有相关的校验，现只是放了一个空方法，如果需要就实现
                if (!this.CheckInsurAbidance(checkRow))
                {
                    return false;
                }
            }

            return true;
        }
        /// <summary>
        /// 获取草药规格里的数量 liujun add 2023-3-16
        /// </summary>
        /// <param name="drugSpec"></param>
        /// <returns></returns>
        private decimal GetSpecNum(string drugSpec)
        {
            if (CommFunc.StrEmpty(drugSpec))
            {
                return 0;
            }

            bool isDot = false;
            string num = string.Empty;
            foreach (char c in drugSpec)
            {
                int cNum;
                if (int.TryParse(c.ToString(), out cNum))
                {
                    num += c.ToString();
                }
                else if (c == '.')
                {
                    if (isDot)
                    {
                        break;
                    }

                    num += ".";
                    isDot = true;
                }
                else
                {
                    break;
                }
            }

            decimal dNUm;
            if (!decimal.TryParse(num, out dNUm))
            {
                dNUm = 0;
            }
            return dNUm;
        }
        public override bool Verify(OUTP_ORDERS_STANDARD order)
        {
            foreach (OUTP_ORDERS_COSTS_STANDARD cost in order.OrderCosts)
            {
                if (!(CommFunc.StrEmpty(cost.ORDER_CLASS.ToString("")) || CommFunc.StrEmpty(cost.ITEM_NAME.ToString("")) || cost.AMOUNT.ToDecimal(0).Equals(0)))
                    continue;

                throw new MessageException("收费项目" + cost.ITEM_NAME.ToString("") + "类别、名称、数量不能为空或0，请调整后再进行保存！");
            }
            string drugName = order.ORDER_TEXT.ToString("");
            if (CommFunc.StrEmpty(order.FREQUENCY.ToString("")))
            {
                throw new MessageException("药品【" + drugName + "】的频次不能为空，请调整！");
            }

            if (order.PERFORM_TIMES.ToDecimal(0) > 999)
            {
                throw new MessageException("药品【" + drugName + "】的执行次数【" + order.PERFORM_TIMES.ToDecimal(0) + "】过大，请调整！");
            }

            decimal dosageSpec = this.GetSpecNum(order.ITEM_SPEC.ToString(""));
            if (dosageSpec <= 0)
            {
                throw new MessageException("药品【" + drugName + "】的规格不合法！");
            }
            if (order.DOSAGE % dosageSpec != 0)
            {
                throw new MessageException("药品【" + drugName + "】的单次剂量不是规格的倍数，不合法！");
            }
            //if (order.DOSAGE % order.DOSE_PER_UNIT != 0)
            //{
            //    throw new MessageException("药品【" + drugName + "】的单次剂量不是规格的倍数，不合法！");
            //}
            if (!order.ORDER_NO.ToDecimal(-999).Equals(-999) && order.ABIDANCE.ToDecimal(0).Equals(0))
            {
                throw new MessageException("药品【" + drugName + "】的用药天数不能为空！");
            }

            if (order.DOSAGE.ToDecimal(0) <= 0)
            {
                throw new MessageException("药品【" + drugName + "】的单次剂量无效！");
            }

            // 验证单次用量是否超标
            if (order.DOSE_PER_UNIT.ToDecimal(0) > 0 && order.AMOUNT_PER_PACKAGE.ToDecimal(0) > 0)
            {
                // 单次剂量是否大于总药品剂量
                if (order.DOSAGE.ToDecimal(0) > order.AMOUNT.ToDecimal(0) * order.DOSE_PER_UNIT.ToDecimal(0) * order.AMOUNT_PER_PACKAGE.ToDecimal(0))
                {
                    throw new MessageException("所开药品【" + drugName + "】的单次用量不能大于药品总剂量，请调整！");
                }
            }

            //====================================================================
            //所开药品为1盒时，不判断药品天数是否超出范围
            //====================================================================
            if (this.prescMaxDays > 0 && order.ABIDANCE.ToInt(0) > this.prescMaxDays && order.AMOUNT.ToDecimal(1) != 1)
            {
                throw new MessageException("药品【" + drugName + "】的药品：【" + drugName + "】用药天数大于处方允许天数：" + this.prescMaxDays + "，请调整后保存");
            }

            // 校验药品是否有库存是否充足
            string drugCode = order.ORDER_CODE.ToString("");
            string drugSpec = order.ITEM_SPEC.ToString("");
            string firmId = order.FIRM_ID.ToString("");
            string dispensary = order.PERFORMED_BY.ToString("");
            decimal quantity = DrugDictDal.GetDrugInventoryInStorage(drugCode, drugSpec, firmId, dispensary);

            if (quantity <= 0)
            {
                throw new MessageException("药品【" + drugName + "】的药局中药品【" + drugName + "】不可供，请确认！");
            }

            decimal quantity1 = PrescParameter.CheckModel == "2" ? DrugDictDal.GetDrugInventoryInNotBilling(drugCode, drugSpec, firmId, dispensary) : 0;
            decimal quantity2 = PrescParameter.CheckModel == "1" || PrescParameter.CheckModel == "2" ? DrugDictDal.GetDrugInventoryInDispensing(drugCode, drugSpec, firmId, dispensary) : 0;


            if (quantity - quantity1 - quantity2 < order.AMOUNT.ToDecimal(0))
            {
                throw new MessageException(string.Format("药品【{0}】库存不足，只能开{1:F2}{3}\n预扣库存{2:F2}{3}！",
                    drugName,
                    (quantity - quantity1 - quantity2).ToString(),
                        (quantity1 + quantity2).ToString(),
                    order.UNITS)
                    );
            }

            //6.6.2有对药品用药天数的的校验，6.6.4没有相关的校验，现只是放了一个空方法，如果需要就实现
            if (!this.CheckInsurAbidance(order))
            {
                return false;
            }
            return true;
        }

        public void SetSubPresc(List<OUTP_ORDERS_STANDARD> addOrders, List<OUTP_ORDERS_STANDARD> groupOrders)
        {
        }

        public List<DeptDict> GetDrugStore()
        {
            List<string> deptCodes = PrescParameter.PRESC_CDRUG_DISPS.Split(' ').ToList();
            using (DeptDictDal deptDictDal = new DeptDictDal())
            {
                return deptDictDal.GetDrugStorageDepts(deptCodes);
            }
        }

        public void SetSubPrescFlag(List<OUTP_ORDERS_STANDARD> orders)
        {

        }

        public List<CDRUG_PROJECT_MASTER> GetCdrugProjectMaster(string deptCode, string uSER_NAME, string hisUnitCode)
        {
            string sqlWhere = "((CREATOR =:CREATOR  OR FLAG = :FLAG1 OR (FLAG = :FLAG2 AND DEPT_CODE =:DEPT_CODE)) AND his_unit_code = :HIS_UNIT_CODE)";
            List<DbParameter> dbParameters = new List<DbParameter>
            {
                CdrugProjectMasterDal.CreateDbParameter("CREATOR",uSER_NAME),
                CdrugProjectMasterDal.CreateDbParameter("FLAG1",1),
                CdrugProjectMasterDal.CreateDbParameter("FLAG2",2),
                CdrugProjectMasterDal.CreateDbParameter("DEPT_CODE",deptCode),
                CdrugProjectMasterDal.CreateDbParameter("HIS_UNIT_CODE",hisUnitCode),
            };
            List<CDRUG_PROJECT_MASTER> masters = this.CdrugProjectMasterDal.GetModelListBySql(sqlWhere, dbParameters);
            List<decimal> treatProjectId = masters.Select(s => s.TREAT_PROJECT_ID.ToDecimal()).ToList();
            List<CDRUG_PROJECT_ITEMS> items = this.CdrugProjectItemsDal.GetList(treatProjectId,SystemParm.LoginUser.USER_NAME);
            foreach (CDRUG_PROJECT_MASTER masterItem in masters)
            {
                masterItem.ITEMS = items.Where(
                                        projectItem => 
                                        projectItem.TREAT_PROJECT_ID == masterItem.TREAT_PROJECT_ID && 
                                        projectItem.CREATOR == masterItem.CREATOR)
                                        .ToList();
            }
            return masters;
        }

        public List<V_CDRUG_DICT> GetCDrugDicts(string storage)
        {
            return cDrugDictDal.GetModelListGroupBy(new V_CDRUG_DICT { STORAGE = storage });
        }

        public List<OUTP_ORDERS_STANDARD> GetPrescList(List<OUTP_ORDERS_STANDARD> orders)
        {
            var list = orders.Where(order => OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE.Equals(order.ORDER_CLASS)).GroupBy(g => g.APPOINT_NO).Select(s => new { orders = s.ToList() } ).ToList();
            List<OUTP_ORDERS_STANDARD> prescList = new List<OUTP_ORDERS_STANDARD>();
            foreach (var item in list)
            {
                if(item.orders != null && item.orders.Count > 0)
                {
                    prescList.Add(item.orders[0]);
                }
            }

            // 修复：按处方号降序排列，让最新的处方显示在最前面
            return prescList.OrderByDescending(p => p.APPOINT_NO).ToList();
        }

        public void SetDecoctionItemCode(List<OUTP_ORDERS_STANDARD> orders, OUTP_ORDERS_STANDARD order)
        {
            if (!string.IsNullOrEmpty(order.DECOCTION_ITEM_CODE))
            {
                return;
            }
            OUTP_ORDERS_STANDARD orderDecoction = orders.FirstOrDefault(o => o.APPOINT_NO.ToString("").Equals(order.APPOINT_NO) && OrderClassDict.ORDER_CLASS_TREAT.Equals(o.ORDER_CLASS));
            if(null != orderDecoction)
            {
                order.DECOCTION_ITEM_CODE = orderDecoction.ORDER_CODE;
            }
        }

        public DataTable GetDecoctionVsChargeDict()
        {
            return this.VstaticDecoctionVsChargeDal.GetList();
        }

        public DataTable GetUsageDesc()
        {
            return this.CDrugUsageDescDal.GetList();
        }

        public InputResult CreateInputResult(V_CDRUG_DICT drugDict)
        {
            InputResult result = new InputResult();
            result.ItemCode = drugDict.DRUG_CODE;
            result.ItemClass = OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE;
            result.ItemName = drugDict.DRUG_NAME;

            // 处理草药字典中 performedBy 为空的情况
            result.Performed_dept = drugDict.STORAGE;
            if (string.IsNullOrEmpty(result.Performed_dept))
            {
                // 草药字典的执行科室为空时，需要在调用方设置默认值
                // 这里暂时设置为空，让调用方处理
                result.Performed_dept = string.Empty;
            }

            result.Performed_name = this.deptDictDal.GetName(result.Performed_dept);
            result.ItemSpec = drugDict.DRUG_SPEC;
            result.ItemFirm = drugDict.FIRM_ID;
            result.ItemPackageUnits = drugDict.PACKAGE_UNITS;
            result.InsureCode = drugDict.INSUR_CODE;
            result.InsureName = drugDict.INSUR_NAME;
            result.ItemPackageSpec = drugDict.PACKAGE_SPEC;
            result.Units = drugDict.UNITS;
            return result;
        }

        public void SetCDrugProperty(OUTP_ORDERS_STANDARD prescListOrder, OUTP_ORDERS_STANDARD cdrugOrder)
        {
            cdrugOrder.FREQUENCY = prescListOrder.FREQUENCY;
            cdrugOrder.HERBAL_DOSAGE = prescListOrder.HERBAL_DOSAGE;
            cdrugOrder.HERBAL_EXTRACT_JUICE_QUANTITY = prescListOrder.HERBAL_EXTRACT_JUICE_QUANTITY;
            cdrugOrder.USAGE_DESC = prescListOrder.USAGE_DESC;
            cdrugOrder.DIAGNOSIS_DESC = prescListOrder.DIAGNOSIS_DESC;
            cdrugOrder.HERBAL_RULES_OF_TREATMENT = prescListOrder.HERBAL_RULES_OF_TREATMENT;
            cdrugOrder.DECOCTION = prescListOrder.DECOCTION;
            cdrugOrder.DECOCTION_ITEM_CODE = prescListOrder.DECOCTION_ITEM_CODE;
            cdrugOrder.APPOINT_NO = prescListOrder.APPOINT_NO;
            cdrugOrder.SERIAL_NO = prescListOrder.SERIAL_NO;
            cdrugOrder.OUTP_SERIAL_NO = prescListOrder.OUTP_SERIAL_NO;
            cdrugOrder.PRESC_COMM_TABOO = prescListOrder.PRESC_COMM_TABOO;
            cdrugOrder.CPRESC_NAME = prescListOrder.CPRESC_NAME;
            cdrugOrder.OrderCosts.ForEach(costs =>
            {
                costs.OUTP_SERIAL_NO = prescListOrder.OUTP_SERIAL_NO;
                costs.SERIAL_NO = prescListOrder.SERIAL_NO;
            });
            this.SetAmout(prescListOrder, cdrugOrder);
        }

        private void SetAmout(OUTP_ORDERS_STANDARD prescListOrder, OUTP_ORDERS_STANDARD item)
        {
            string drugCode = string.Empty;
            string drugName = string.Empty;
            string drugSpec = string.Empty;
            string firmId = string.Empty;
            string units = string.Empty;
            decimal price = 0;
            decimal chargePrice = 0;
            decimal amount = prescListOrder.AMOUNT.ToDecimal(0);
            decimal dosage = 0;
            decimal doscPerUnit = 0;
            decimal amountPerPackage = 0;
            item.REPETITION = prescListOrder.REPETITION;

            if (CommFunc.StrEmpty(item.ORDER_CODE.ToString("")))
                return;

            // 添加调试日志：记录SetAmout方法调用
            drugName = item.ORDER_TEXT.ToString("");
            drugCode = item.ORDER_CODE.ToString("");
            decimal originalDosage = item.DOSAGE.ToDecimal(0);
            WriteDebugLog($"[CDrugPresc.SetAmout] 方法开始 - 药品名称: {drugName}, 药品代码: {drugCode}, 进入时dosage: {originalDosage}g");

            amount = item.AMOUNT.ToDecimal(0);

            // 修复：对于复制的历史处方，如果原始dosage > 1g，则保护这个值
            // 避免CalculateSplitDosage重新计算导致dosage被重置
            if (originalDosage > 1.0m)
            {
                // 检查是否为复制的历史处方（通过inputResult集合判断）
                string itemPackageSpec = item.ITEM_SPEC.ToString("");
                string itemFirmId = item.FIRM_ID.ToString("");
                string itemUnits = item.UNITS.ToString("");
                bool isFromHistory = !inputResult.Any(s => s.ItemCode == drugCode && s.ItemName == drugName &&
                                                          s.ItemPackageSpec == itemPackageSpec && s.ItemFirm == itemFirmId &&
                                                          s.ItemPackageUnits == itemUnits);

                if (isFromHistory)
                {
                    WriteDebugLog($"[CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: {drugName}, 保持原始剂量: {originalDosage}g, 跳过CalculateSplitDosage计算");
                    dosage = originalDosage;
                }
                else
                {
                    dosage = this.CalculateSplitDosage(item);
                    WriteDebugLog($"[CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: {drugName}, CalculateSplitDosage返回: {dosage}g");
                }
            }
            else
            {
                dosage = this.CalculateSplitDosage(item);
                WriteDebugLog($"[CDrugPresc.SetAmout] CalculateSplitDosage返回 - 药品名称: {drugName}, 返回dosage: {dosage}g, 原始dosage: {originalDosage}g");
            }

            item.DOSAGE = dosage;

            doscPerUnit = item.DOSE_PER_UNIT.ToDecimal(0);
            amountPerPackage = item.AMOUNT_PER_PACKAGE.ToDecimal(0);

            drugCode = item.ORDER_CODE.ToString("");
            drugName = item.ORDER_TEXT.ToString("");
            drugSpec = item.ITEM_SPEC.ToString("");
            firmId = item.FIRM_ID.ToString("");
            units = item.UNITS.ToString("");

            //先判断是否已经取过价格
            //取过价格的就不再次取价格
            price = item.ITEM_PRICE.ToDecimal(-1);
            chargePrice = item.CHARGE_PRICE.ToDecimal(-1);
            decimal defaultFactor = 1;
            int proportion_numerator = 0;
            int proportion_denominator = 0;
            decimal personal_price = decimal.Zero;

            if (chargePrice.Equals(-1))
            {
                PlatCommon.Comm.class_obilling_public.f_his21_calc_charge_price_outp(this.OrderBusiness.GetCurrentPatient().CHARGE_TYPE, "B", drugCode, drugSpec, price, defaultFactor, ref chargePrice, ref proportion_numerator, ref proportion_denominator, ref personal_price);
            }
            //if (price.Equals(-1) || chargePrice.Equals(-1))
            //    priceListDal.CalcPriceByChargeItem(this.PatientInfo, "B", drugCode, drugName, drugSpec + firmId, units, ref price, ref chargePrice);

            if (amountPerPackage.Equals(0))
            {
                amountPerPackage = DrugDictDal.GetDrugAmountPerPackage(drugCode, drugSpec, units, firmId);
                doscPerUnit = DrugDictDal.GetDrugDosePerUnit(drugCode, drugSpec);
                item.AMOUNT_PER_PACKAGE = amountPerPackage;
            }

            item.PRICE = price;
            item.CHARGE_PRICE = chargePrice;


            amount = item.REPETITION.ToInt(1) * dosage / (doscPerUnit * amountPerPackage);

            item.AMOUNT = Math.Round(amount, 4);
            item.COSTS = Math.Round(amount * price, PrescParameter.CostsPrecision);
            item.CHARGES = Math.Round(amount * chargePrice, PrescParameter.ChargesPrecision);

            if (item.OrderCosts != null && item.OrderCosts.Count > 0)
            {
                OUTP_ORDERS_COSTS_STANDARD costItem = item.OrderCosts[0];
                costItem.AMOUNT = Math.Round(amount, 4);
                costItem.COSTS = Math.Round(amount * price, PrescParameter.CostsPrecision);
                costItem.CHARGES = Math.Round(amount * chargePrice, PrescParameter.ChargesPrecision);
                costItem.REPETITION = item.REPETITION.ToInt(1);
            }

            item.ABIDANCE = item.REPETITION.ToInt(1);
        }

        /// <summary>
        /// 煎法字典
        /// </summary>
        /// <returns></returns>
        public DataTable GetDecoctionDict()
        {
            string strDecoc = PrescParameter.CDRUG_FREQ_DETAIL;
            if (string.IsNullOrEmpty(strDecoc))
                strDecoc = ";先煎;后下;包煎;烊化冲入;煎汤代水;溶化;另煎后兑入;生汁兑入;合药冲服";
            string[] sa = strDecoc.Split(';');
            DataTable dtRet = new DataTable();
            dtRet.Columns.Add("ITEM_NAME");
            foreach (string s in sa)
            {
                dtRet.Rows.Add(s);
            }
            return dtRet;
        }

        /// <summary>
        /// 草药特殊要求
        /// </summary>
        /// <returns></returns>
        public DataTable GetSpecialRequestDict()
        {
            string strDecoc = PrescParameter.CDRUG_SPECIAL_REQUEST;
            if (string.IsNullOrEmpty(strDecoc))
                strDecoc = ";烘烤;粉碎";
            string[] sa = strDecoc.Split(';');
            DataTable dtRet = new DataTable();
            dtRet.Columns.Add("SPECIAL_REQUEST");
            foreach (string s in sa)
            {
                dtRet.Rows.Add(s);
            }
            return dtRet;
        }

        public bool isNeedSave(List<OUTP_ORDERS_STANDARD> prescList, List<OUTP_ORDERS_STANDARD> prescOrders)
        {
            bool prescListState = prescList.Count(c => Constants.NEW_ORDER_STATE_STR.Equals(c.STATE)) > 0;
            bool prescState = prescOrders.Count(c => c.STATE.Equals(Constants.NEW_ORDER_STATE_STR)) > 0;
            return prescListState || prescState;
        }

        public InputResult CreateInputResult(CDRUG_PROJECT_ITEMS item,string dispensary)
        {
            DataTable dtDrugInfo = priceListDal.GetCdrugSpec(item.CDRUG_CODE, dispensary);
            if(dtDrugInfo == null || dtDrugInfo.Rows.Count == 0)
            {
                XtraMessageBox.Show($"药品[{item.CDRUG_NAME}]在所选药局不可供");
                return null;
            }
            DataRow dataRow = dtDrugInfo.Rows[0];
            InputResult result = new InputResult();
            result.ItemCode = item.CDRUG_CODE;
            result.ItemClass = OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE;
            result.ItemName = item.CDRUG_NAME;
            result.Performed_dept = dispensary;//item.STORAGE;
            result.Performed_name = this.deptDictDal.GetName(dispensary);
            result.ItemSpec = dataRow["DRUG_SPEC"].ToString();// item.DRUG_SPEC;
            result.ItemUnit = dataRow["DRUG_SPEC"].ToString();// item.DRUG_SPEC;
            result.ItemPackageSpec= dataRow["PACKAGE_SPEC"].ToString(); 
            result.ItemPackageUnits = dataRow["PACKAGE_UNITS"].ToString();
            result.Units = dataRow["UNITS"].ToString();
            result.ItemFirm = dataRow["FIRM_ID"].ToString();//item.FIRM_ID;
            result.Dose_per_unit = item.DOSAGE;
            result.LabAddFromType = Enums.AddFromType.Template;
            result.InsureCode = dataRow["GJBM"].ToString();
            result.InsureName = dataRow["GJMC"].ToString();
            return result;
        }

        public OUTP_ORDERS_STANDARD NewPresc(OUTP_ORDERS_STANDARD order, string deptCode)
        {
            order.ORDER_CLASS = OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE;
            order.APPOINT_NO = "新方";
            order.PERFORMED_BY = deptCode;
            order.USAGE_DESC = "开水冲服，每日2次，每次1包";
            order.FREQUENCY = "2/日";
            order.PRESC_ATTR = PrescParameter.PrescAttr;
            return order;
        }


        // 反射实现深拷贝
        public T DeepCopyByReflect<T>(T obj)
        {
            //如果是字符串或值类型则直接返回
            if (obj is string || obj.GetType().IsValueType) return obj;

            object retval = Activator.CreateInstance(obj.GetType());
            FieldInfo[] fields = obj.GetType().GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Static);
            foreach (FieldInfo field in fields)
            {
                // 解决层次引用类型
                try
                {
                    if (field.GetValue(obj) == null)
                    {
                        field.SetValue(retval, null);
                    }
                    else
                    {
                        field.SetValue(retval, DeepCopyByReflect(field.GetValue(obj)));
                    }
                }
                catch { }
            }
            return (T)retval;
        }

        // 中药开处方时用于药品数量变化时一条处方变多条，中药未用
        public bool GetAmountChangeOrders(OUTP_ORDERS_STANDARD order, List<OUTP_ORDERS_STANDARD> orderList, List<OUTP_ORDERS_STANDARD> addOrder, ref List<OUTP_ORDERS_STANDARD> addOrderList, ref List<OUTP_ORDERS_STANDARD> removeOrderList,ref string strMsg)
        {
            bool bResult = true;
            decimal amount = order.AMOUNT.ToDecimal(0);
            string drugCode = order.ORDER_CODE.ToString("");
            string drugName = order.ORDER_TEXT.ToString("");
            string itemPackageSpec = order.ITEM_SPEC.ToString("");
            string drugSpec = order.DRUG_SPEC.ToString("");
            string firmId = order.FIRM_ID.ToString("");
            string units = order.UNITS.ToString("");
            decimal price = order.PRICE.ToDecimal(-1);
            decimal chargePrice = order.CHARGE_PRICE.ToDecimal(-1);
            decimal order_no = order.ORDER_NO;
            decimal order_sub_no= order.ORDER_SUB_NO+1;

            // 查找药品
            InputResult inputRet = inputResult.Where(s => s.ItemCode == drugCode && s.ItemName == drugName && s.ItemPackageSpec == itemPackageSpec && s.ItemFirm == firmId && s.ItemPackageUnits == units).ToList().FirstOrDefault();
            if (inputRet == null)
            {
                // 修复：对于复制的历史处方，inputResult集合中可能没有药品信息
                // 这种情况下，我们跳过进定销策略的处理，直接返回成功
                // 因为复制的历史处方已经有完整的药品信息，不需要重新计算

                // 添加调试日志
                WriteDebugLog($"[CDrugPresc.GetAmountChangeOrders] 检查点[跳过进定销处理] - 药品名称: {drugName}, 药品代码: {drugCode}, 原因: 复制的历史处方，inputResult中无此药品信息");

                return true;
            }

            // 只有当inputRet不为null时，才进行进定销策略处理
            // 合并order
            // 1.以前就分过
            //if (order.SPLIT_SNO != null && order.SPLIT_SNO >= 0)
            //{
            //    // 先合并orders
            //    List<OUTP_ORDERS_STANDARD> orderMergeList = orderList.Where(r => r.SPLIT_SNO.Equals(order.SPLIT_SNO)).ToList();
            //    decimal totalMount = 0;
            //    foreach (OUTP_ORDERS_STANDARD orderMerge in orderMergeList)
            //    {
            //        totalMount += Convert.ToDecimal(orderMerge.AMOUNT);
            //        removeOrderList.Add(orderMerge);
            //    }

            //    int iRetCode = 0;
            //    DataTable dtYJDX = PurchaseBasedSales.GetMayUseDrugStockInfo(inputRet.Performed_dept, drugCode, drugSpec, firmId, inputRet.ItemPackageSpec, inputRet.ItemPackageUnits, string.Empty, inputRet.Units, totalMount, out iRetCode);
            //    if (iRetCode == -2)
            //    {
            //        order.DOSAGE = 1;
            //        this.AmountChanged(order, addOrder);
            //        strMsg = $"药品[{inputRet.InsureName}]数量不足！";
            //        return false;
            //    }
            //    else if (iRetCode == -1)
            //    {
            //        order.DOSAGE = 1;
            //        this.AmountChanged(order, addOrder);
            //        strMsg = $"未找到药品[{inputRet.InsureName}]！";
            //        return false;
            //    }
            //    else
            //    {
            //        if (dtYJDX != null)
            //        {
            //            if (dtYJDX.Rows.Count > 1)
            //            {
            //                int maxSplitNo = Convert.ToInt32(orderList.Max(r => r.SPLIT_SNO) + 1);

            //                IOrderCosts costs = new OrderCostsBusiness();

            //                foreach (DataRow dr in dtYJDX.Rows)
            //                {
            //                    string storage2 = Converter.ToString(dr["STORAGE"]);
            //                    string drugCode2 = Converter.ToString(dr["DRUG_CODE"]);
            //                    string drugSpec2 = Converter.ToString(dr["DRUG_SPEC"]);
            //                    string firmId2 = Converter.ToString(dr["FIRM_ID"]);
            //                    string packageSpec2 = Converter.ToString(dr["PACKAGE_SPEC"]);
            //                    string packageUnits2 = Converter.ToString(dr["PACKAGE_UNITS"]);
            //                    string batchCode2 = Converter.ToString(dr["BATCH_CODE"]);
            //                    string batchNo2 = Converter.ToString(dr["BATCH_NO"]);
            //                    decimal amount2 = Converter.ToDecimal(dr["QUANTITY"]);
            //                    string units2 = Converter.ToString(dr["UNITS"]);
            //                    decimal price2 = Convert.ToDecimal(dr["RETAIL_PRICE"]);
            //                    decimal chargePrice2 = Convert.ToDecimal(dr["RETAIL_PRICE"]);


            //                    //if (!priceListDal.GetClinicItemPrice(inputRet, OrderApply.PatientInfo.CHARGE_TYPE, ref price2, ref chargePrice2))
            //                    //{
            //                    //    throw new MessageException("获取【编码：" + inputRet.ItemCode + "】，【名称：" + inputRet.ItemName + "】的药品的价格信息出错!");
            //                    //}

            //                    OUTP_ORDERS_STANDARD order1 = DeepCopyByReflect(order);
            //                    order1.ORDER_CODE = drugCode2;
            //                    order1.DRUG_SPEC = drugSpec2;
            //                    order1.FIRM_ID = firmId2;
            //                    order1.ITEM_SPEC = packageSpec2;
            //                    order1.UNITS = packageUnits2;
            //                    order1.BATCH_CODE = batchCode2;
            //                    order1.BATCH_NO = batchNo2;
            //                    order1.AMOUNT = amount2;
            //                    order1.SPLIT_SNO = maxSplitNo;
            //                    order.ORDER_SUB_NO = order_sub_no++;

            //                    // 重新计算价格
            //                    order1.ITEM_PRICE = price2;
            //                    order1.PRICE = price2;
            //                    order1.CHARGE_PRICE = chargePrice2;
            //                    order1.COSTS = Math.Round(amount2 * price2, 4);
            //                    order1.CHARGES = Math.Round(amount2 * chargePrice2, 4);

            //                    if (order1.OrderCosts != null)
            //                    {
            //                        order1.OrderCosts = null;
            //                    }

            //                    costs.AddOutpCostsByOrder(OrderApply.PatientInfo, order1, false, true, "J", false);

            //                    addOrderList.Add(order1);
            //                }
            //            }
            //            if (dtYJDX.Rows.Count == 1)
            //            {
            //                DataRow dr = dtYJDX.Rows[0];
            //                IOrderCosts costs = new OrderCostsBusiness();

            //                string storage2 = Converter.ToString(dr["STORAGE"]);
            //                string drugCode2 = Converter.ToString(dr["DRUG_CODE"]);
            //                string drugSpec2 = Converter.ToString(dr["DRUG_SPEC"]);
            //                string firmId2 = Converter.ToString(dr["FIRM_ID"]);
            //                string packageSpec2 = Converter.ToString(dr["PACKAGE_SPEC"]);
            //                string packageUnits2 = Converter.ToString(dr["PACKAGE_UNITS"]);
            //                string batchCode2 = Converter.ToString(dr["BATCH_CODE"]);
            //                string batchNo2 = Converter.ToString(dr["BATCH_NO"]);
            //                decimal amount2 = Converter.ToDecimal(dr["QUANTITY"]);
            //                string units2 = Converter.ToString(dr["UNITS"]);
            //                decimal price2 = Convert.ToDecimal(dr["RETAIL_PRICE"]);
            //                decimal chargePrice2 = Convert.ToDecimal(dr["RETAIL_PRICE"]);

            //                OUTP_ORDERS_STANDARD order1 = DeepCopyByReflect(order);
            //                order1.ORDER_CODE = drugCode2;
            //                order1.DRUG_SPEC = drugSpec2;
            //                order1.FIRM_ID = firmId2;
            //                order1.ITEM_SPEC = packageSpec2;
            //                order1.UNITS = packageUnits2;
            //                order1.BATCH_CODE = batchCode2;
            //                order1.BATCH_NO = batchNo2;
            //                order1.AMOUNT = amount2;
            //                order1.SPLIT_SNO = null;
            //                order.ORDER_SUB_NO = order_sub_no++;

            //                // 重新计算价格
            //                order1.ITEM_PRICE = price2;
            //                order1.PRICE = price2;
            //                order1.CHARGE_PRICE = chargePrice2;
            //                order1.COSTS = Math.Round(amount2 * price2, 4);
            //                order1.CHARGES = Math.Round(amount2 * chargePrice2, 4);

            //                if (order1.OrderCosts != null)
            //                {
            //                    order1.OrderCosts = null;
            //                }

            //                costs.AddOutpCostsByOrder(OrderApply.PatientInfo, order1, false, true, "J", false);

            //                addOrderList.Add(order1);

            //                foreach (OUTP_ORDERS_STANDARD orderMerge in orderMergeList)
            //                {
            //                    removeOrderList.Add(orderMerge);
            //                }
            //            }
            //        }
            //    }
            //}
            //// 2.以前没分过
            //else
            //{
            // 合并order
            // 1.以前就分过
            if (order.SPLIT_SNO != null && order.SPLIT_SNO >= 0)
            {
                // 先合并orders
                List<OUTP_ORDERS_STANDARD> orderMergeList = orderList.Where(r => r.SPLIT_SNO.Equals(order.SPLIT_SNO)).ToList();
                decimal totalMount = 0;
                foreach (OUTP_ORDERS_STANDARD orderMerge in orderMergeList)
                {
                    totalMount += Convert.ToDecimal(orderMerge.AMOUNT);
                    removeOrderList.Add(orderMerge);
                }

                int iRetCode = 0;
                string strMessage = "";
                DataTable dtYJDX = PurchaseBasedSales.GetMayUseDrugStockInfo(inputRet.Performed_dept, drugCode, drugSpec, firmId, inputRet.ItemPackageSpec, inputRet.ItemPackageUnits, string.Empty, inputRet.Units, totalMount, out iRetCode, out strMessage);
                if (iRetCode == -2)
                {
                    order.DOSAGE = 1;
                    this.AmountChanged(order, addOrder);
                    strMsg = $"药品[{inputRet.ItemName}]数量不足！";
                    return false;
                }
                else if (iRetCode == -1)
                {
                    // 修复：对于"未找到药品"错误，直接返回成功，不显示错误消息
                    // 这通常发生在复制历史处方时，是正常情况

                    // 添加调试日志
                    WriteDebugLog($"[CDrugPresc.GetAmountChangeOrders] 检查点[跳过进定销错误] - 药品名称: {drugName}, 药品代码: {drugCode}, 原因: 复制的历史处方，跳过进定销策略失败错误");

                    return true;
                }
                else
                {
                    if (dtYJDX != null && dtYJDX.Rows.Count >= 1)
                    {
                        int maxSplitNo = Convert.ToInt32(orderList.Max(r => r.SPLIT_SNO) + 1);

                        IOrderCosts costs = new OrderCostsBusiness();

                        foreach (DataRow dr in dtYJDX.Rows)
                        {
                            string storage2 = Converter.ToString(dr["STORAGE"]);
                            string drugCode2 = Converter.ToString(dr["DRUG_CODE"]);
                            string drugSpec2 = Converter.ToString(dr["DRUG_SPEC"]);
                            string firmId2 = Converter.ToString(dr["FIRM_ID"]);
                            string packageSpec2 = Converter.ToString(dr["PACKAGE_SPEC"]);
                            string packageUnits2 = Converter.ToString(dr["PACKAGE_UNITS"]);
                            string batchCode2 = Converter.ToString(dr["BATCH_CODE"]);
                            string batchNo2 = Converter.ToString(dr["BATCH_NO"]);
                            decimal amount2 = Converter.ToDecimal(dr["QUANTITY"]);
                            string units2 = Converter.ToString(dr["UNITS"]);
                            decimal price2 = Convert.ToDecimal(dr["RETAIL_PRICE"]);
                            decimal chargePrice2 = Convert.ToDecimal(dr["RETAIL_PRICE"]);

                            OUTP_ORDERS_STANDARD order1 = DeepCopyByReflect(order);
                            order1.ORDER_CODE = drugCode2;
                            order1.DRUG_SPEC = drugSpec2;
                            order1.FIRM_ID = firmId2;
                            order1.ITEM_SPEC = packageSpec2;
                            order1.UNITS = packageUnits2;
                            order1.BATCH_CODE = batchCode2;
                            order1.BATCH_NO = batchNo2;
                            order1.AMOUNT = amount2;
                            order1.SPLIT_SNO = maxSplitNo;
                            order.ORDER_SUB_NO = order_sub_no++;

                            // 重新计算价格
                            order1.ITEM_PRICE = price2;
                            order1.PRICE = price2;
                            order1.CHARGE_PRICE = chargePrice2;
                            order1.COSTS = Math.Round(amount2 * price2, 4);
                            order1.CHARGES = Math.Round(amount2 * chargePrice2, 4);

                            if (order1.OrderCosts != null)
                            {
                                order1.OrderCosts = null;
                            }

                            costs.AddOutpCostsByOrder(OrderApply.PatientInfo, order1, false, true, "J", false);

                            addOrderList.Add(order1);
                        }

                        removeOrderList.Add(order);
                    }
                }
            }
           // }
            return true;
        }

        // 用于药品数量变化时一条处方变多条
        public void ClearInputResult()
        {
            inputResult.Clear();
        }

        private static void WriteDebugLog(string message)
        {
            try
            {
                string logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "LOG", "exLOG");
                if (!Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                }
                string logFile = Path.Combine(logDirectory, $"PrescBusiness_Debug_{DateTime.Now:yyyyMMdd}.log");
                string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] {message}";
                File.AppendAllText(logFile, logEntry + Environment.NewLine);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"日志写入失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取当前患者的最大ITEM_NO，确保唯一性
        /// </summary>
        /// <returns></returns>
        private decimal GetMaxItemNoForPatient()
        {
            try
            {
                // 如果缓存为空，从数据库获取当前患者的最大ITEM_NO
                if (_cachedMaxItemNo == null)
                {
                    string strSql = @"SELECT NVL(MAX(ITEM_NO), 0) FROM OUTPDOCT.OUTP_ORDERS_STANDARD
                                     WHERE PATIENT_ID = :t1 AND CLINIC_NO = :t2";

                    var db = new Dal.OracleDataHelper();
                    List<System.Data.Common.DbParameter> listPara = new List<System.Data.Common.DbParameter>();
                    listPara.Add(db.CreateDbParameter(":t1", OrderApply.PatientInfo.PATIENT_ID));
                    listPara.Add(db.CreateDbParameter(":t2", OrderApply.PatientInfo.CLINIC_NO));

                    _cachedMaxItemNo = Convert.ToDecimal(db.GetSingleValue(strSql, listPara) ?? 0);
                }

                return _cachedMaxItemNo.Value;
            }
            catch (Exception ex)
            {
                // 如果获取失败，返回0作为默认值
                return 0;
            }
        }

        /// <summary>
        /// 重置ITEM_NO缓存
        /// </summary>
        public static void ResetItemNoCache()
        {
            _cachedMaxItemNo = null;
        }
    }
}
