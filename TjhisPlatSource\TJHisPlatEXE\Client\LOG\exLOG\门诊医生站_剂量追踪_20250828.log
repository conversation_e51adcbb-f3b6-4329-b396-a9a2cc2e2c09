[2025-08-28 17:05:51] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8091, REPETITION=1, DOSAGE=1g, CurrentPresc.DOSAGE=1g
[2025-08-28 17:05:51] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8090, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-28 17:05:51] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:05:51] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:05:51] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-28 17:05:51] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=6742, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-28 17:05:51] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=枸杞子, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:05:51] [DEBUG] [SetAmout入口] 药品=枸杞子, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:05:51] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=枸杞子, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-28 17:05:51] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8090, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-28 17:05:51] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:05:51] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:05:51] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-28 17:05:54] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8090, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-28 17:05:54] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=6742, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-28 17:05:54] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=枸杞子, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:05:54] [DEBUG] [SetAmout入口] 药品=枸杞子, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:05:54] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=枸杞子, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-28 17:05:54] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=6742, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-28 17:05:54] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=枸杞子, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:05:54] [DEBUG] [SetAmout入口] 药品=枸杞子, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:05:54] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=枸杞子, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-28 17:05:54] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=6742, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-28 17:05:54] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=枸杞子, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:05:54] [DEBUG] [SetAmout入口] 药品=枸杞子, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:05:54] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=枸杞子, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-28 17:06:02] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=1.0g, 处方列表dosage=g, item.AMOUNT=1, prescListOrder.AMOUNT=
[2025-08-28 17:06:10] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=g, item.AMOUNT=1, prescListOrder.AMOUNT=
[2025-08-28 17:06:10] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:06:11] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:06:12] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=新方, REPETITION=1, DOSAGE=g, CurrentPresc.DOSAGE=g
[2025-08-28 17:06:13] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8092, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-28 17:06:13] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:06:13] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:06:13] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-28 17:06:14] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=6742, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-28 17:06:14] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=枸杞子, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:06:14] [DEBUG] [SetAmout入口] 药品=枸杞子, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:06:14] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=枸杞子, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-28 17:06:14] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8092, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-28 17:06:14] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:06:14] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:06:14] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-28 17:06:15] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8092, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-28 17:06:15] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:06:15] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:06:15] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-28 17:06:15] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8092, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-28 17:06:15] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:06:15] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-28 17:06:15] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-28 17:06:21] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=1.0g, 处方列表dosage=g, item.AMOUNT=1, prescListOrder.AMOUNT=
[2025-08-28 17:06:27] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=12g, 处方列表dosage=g, item.AMOUNT=1, prescListOrder.AMOUNT=
[2025-08-28 17:06:27] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=12g, 处方列表dosage=g, item.AMOUNT=12, prescListOrder.AMOUNT=
